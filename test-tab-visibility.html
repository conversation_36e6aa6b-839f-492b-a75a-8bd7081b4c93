<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .status {
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .visible {
            background-color: #22c55e;
            color: white;
        }
        .hidden {
            background-color: #ef4444;
            color: white;
        }
        .warning {
            background-color: #f59e0b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 2px solid #fbbf24;
        }
        .log {
            background-color: #374151;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
        #logs {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #4b5563;
            border-radius: 4px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>Tab Visibility API Test</h1>
    <p>This page tests the Page Visibility API functionality that we've implemented in the streaming application.</p>
    
    <div id="status" class="status visible">
        Tab is currently: <span id="visibility-state">visible</span>
    </div>
    
    <div id="tab-warning" class="warning" style="display: none;">
        ⚠️ Tab Backgrounded: To maintain stream quality, please keep this tab active and visible.
        <br><small>📱 Browsers throttle video/audio processing when tabs are in the background, which may affect your stream.</small>
    </div>
    
    <div id="connection-warning" class="warning" style="display: none;">
        <strong>Connection Issue:</strong> Stream may be interrupted
        <div id="connection-message" class="mt-2 text-sm"></div>
    </div>

    <h3>Instructions:</h3>
    <ol>
        <li>Click "Simulate Connection Issue" to see the error messages</li>
        <li>Switch to another tab or minimize this window</li>
        <li>Come back to see how the error message transitions smoothly</li>
        <li>Notice the 3-second delay before switching back to internet connectivity message</li>
    </ol>

    <button id="simulate-connection" style="padding: 10px 20px; margin: 10px 0; background-color: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Simulate Connection Issue
    </button>
    <button id="clear-connection" style="padding: 10px 20px; margin: 10px 0; background-color: #22c55e; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Clear Connection Issue
    </button>
    
    <h3>Event Log:</h3>
    <div id="logs"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const visibilityStateSpan = document.getElementById('visibility-state');
        const tabWarning = document.getElementById('tab-warning');
        const connectionWarning = document.getElementById('connection-warning');
        const connectionMessage = document.getElementById('connection-message');
        const logsDiv = document.getElementById('logs');

        let hasConnectionIssue = false;
        let wasTabBackgrounded = false;
        let tabVisibilityTimeout = null;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function updateConnectionMessage() {
            if (!hasConnectionIssue) {
                connectionWarning.style.display = 'none';
                return;
            }

            connectionWarning.style.display = 'block';
            const isVisible = !document.hidden;

            if (!isVisible) {
                connectionMessage.textContent = '📱 Keep this tab active for optimal streaming. Browsers throttle performance when tabs are backgrounded.';
            } else if (isVisible && wasTabBackgrounded) {
                connectionMessage.textContent = '📱 Tab is now active. If connection issues persist, check your internet connection.';
            } else {
                connectionMessage.textContent = '💡 Check your internet connection. The stream will automatically recover when connection is restored.';
            }
        }

        function updateVisibilityStatus() {
            const isVisible = !document.hidden;
            const visibilityState = document.visibilityState;

            visibilityStateSpan.textContent = visibilityState;

            if (isVisible) {
                statusDiv.className = 'status visible';
                tabWarning.style.display = 'none';

                // Handle tab visibility transition logic
                if (wasTabBackgrounded) {
                    log('📱 Tab became visible again after being backgrounded');
                    // Wait 3 seconds before clearing the backgrounded state
                    if (tabVisibilityTimeout) clearTimeout(tabVisibilityTimeout);
                    tabVisibilityTimeout = setTimeout(() => {
                        wasTabBackgrounded = false;
                        updateConnectionMessage();
                        log('🔄 Cleared backgrounded state - messages will now show normal connectivity advice');
                    }, 3000);
                }
            } else {
                statusDiv.className = 'status hidden';
                tabWarning.style.display = 'block';

                // Tab became hidden - immediately mark as backgrounded
                wasTabBackgrounded = true;
                if (tabVisibilityTimeout) {
                    clearTimeout(tabVisibilityTimeout);
                    tabVisibilityTimeout = null;
                }
                log('📱 Tab backgrounded - marked for special error messaging');
            }

            updateConnectionMessage();
            log(`📱 Tab visibility changed: ${visibilityState} (visible: ${isVisible})`);
        }
        
        // Initial status
        updateVisibilityStatus();
        log('🚀 Tab visibility monitoring started');

        // Listen for visibility changes
        document.addEventListener('visibilitychange', updateVisibilityStatus);

        // Button handlers
        document.getElementById('simulate-connection').addEventListener('click', () => {
            hasConnectionIssue = true;
            updateConnectionMessage();
            log('🔴 Simulated connection issue');
        });

        document.getElementById('clear-connection').addEventListener('click', () => {
            hasConnectionIssue = false;
            updateConnectionMessage();
            log('🟢 Cleared connection issue');
        });

        // Log browser support
        if (typeof document.hidden !== 'undefined') {
            log('✅ Page Visibility API is supported');
        } else {
            log('❌ Page Visibility API is not supported');
        }
    </script>
</body>
</html>
