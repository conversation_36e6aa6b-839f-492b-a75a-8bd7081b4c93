<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .status {
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .visible {
            background-color: #22c55e;
            color: white;
        }
        .hidden {
            background-color: #ef4444;
            color: white;
        }
        .warning {
            background-color: #f59e0b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 2px solid #fbbf24;
        }
        .log {
            background-color: #374151;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
        #logs {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #4b5563;
            border-radius: 4px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>Tab Visibility API Test</h1>
    <p>This page tests the Page Visibility API functionality that we've implemented in the streaming application.</p>
    
    <div id="status" class="status visible">
        Tab is currently: <span id="visibility-state">visible</span>
    </div>
    
    <div id="tab-warning" class="warning" style="display: none;">
        ⚠️ Tab Backgrounded: To maintain stream quality, please keep this tab active and visible.
        <br><small>📱 Browsers throttle video/audio processing when tabs are in the background, which may affect your stream.</small>
    </div>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Switch to another tab or minimize this window</li>
        <li>Come back to see the visibility changes logged</li>
        <li>Notice how the warning appears when the tab is backgrounded</li>
    </ol>
    
    <h3>Event Log:</h3>
    <div id="logs"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const visibilityStateSpan = document.getElementById('visibility-state');
        const tabWarning = document.getElementById('tab-warning');
        const logsDiv = document.getElementById('logs');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function updateVisibilityStatus() {
            const isVisible = !document.hidden;
            const visibilityState = document.visibilityState;
            
            visibilityStateSpan.textContent = visibilityState;
            
            if (isVisible) {
                statusDiv.className = 'status visible';
                tabWarning.style.display = 'none';
            } else {
                statusDiv.className = 'status hidden';
                tabWarning.style.display = 'block';
            }
            
            log(`📱 Tab visibility changed: ${visibilityState} (visible: ${isVisible})`);
        }
        
        // Initial status
        updateVisibilityStatus();
        log('🚀 Tab visibility monitoring started');
        
        // Listen for visibility changes
        document.addEventListener('visibilitychange', updateVisibilityStatus);
        
        // Log browser support
        if (typeof document.hidden !== 'undefined') {
            log('✅ Page Visibility API is supported');
        } else {
            log('❌ Page Visibility API is not supported');
        }
    </script>
</body>
</html>
