# 🚀 Media Server Deployment Guide

Simple deployment workflow for the WebRTC media server without requiring Docker or ARM64 development environment.

## 📋 Quick Start

### Prerequisites
- **OCI CLI** configured with your credentials
- **Terraform** installed
- **SSH key** for OCI instances (`~/.ssh/oci_key`)
- **Node.js** (for reading package.json version)

### Basic Deployment

```bash
# Deploy your current media-server code
./scripts/deploy-media-server.sh

# Deploy and create compiled package for future instant deployments
./scripts/deploy-media-server.sh --package
```

## 🔄 Development Workflow

### 1. **Make Changes Locally**
Edit your code in the `media-server/` directory as usual:
```bash
# Edit your media server code
vim media-server/src/server.js
vim media-server/package.json
```

### 2. **Deploy to OCI**
```bash
# Simple deployment (8 minutes compilation)
./scripts/deploy-media-server.sh

# Or create compiled package for future instant deployments
./scripts/deploy-media-server.sh --package
```

### 3. **Monitor Progress**
The script will show you how to monitor the compilation:
```bash
# Monitor deployment logs
ssh -i ~/.ssh/oci_key opc@<server-ip> 'sudo tail -f /var/log/startup-script.log'
```

### 4. **Test Your Changes**
Once deployment completes:
```bash
# Test the server
curl -k https://<server-ip>:8080/health
```

## ⚡ Optimization Strategy

### **First Deployment** (8 minutes)
1. Upload source code to OCI Object Storage
2. Deploy server with Terraform
3. Server compiles mediasoup worker (~8 minutes)
4. Optionally package compiled result

### **Subsequent Deployments** (3 minutes - when compiled package exists)
1. Upload source code
2. Deploy server
3. Server downloads pre-compiled package (instant!)
4. Server starts immediately

### **Creating Compiled Packages**
```bash
# After any deployment, create a compiled package for future speed
./scripts/deploy-media-server.sh --package
```

This downloads the compiled mediasoup worker and uploads it to OCI Artifact Registry for instant future deployments.

## 🛠 How It Works

### **The Script Does:**
1. **Packages** your local `media-server/` directory (excluding `node_modules`)
2. **Uploads** the package to OCI Object Storage
3. **Deploys** using Terraform
4. **Monitors** compilation progress (optional)
5. **Packages** compiled result for future instant deployments (if `--package` flag used)

### **The Server Does:**
1. **Downloads** your source package from OCI Object Storage
2. **Installs** npm dependencies
3. **Compiles** mediasoup worker for ARM64 (~8 minutes)
4. **Starts** the media server
5. **Serves** WebRTC connections on port 8080

### **Future Deployments:**
1. Server **tries to download** pre-compiled package first
2. If found: **instant deployment** (~3 minutes total)
3. If not found: **falls back** to compilation (~8 minutes)

## 📁 File Structure

```
poc-webrtc/
├── media-server/              # Your source code
│   ├── src/
│   ├── package.json
│   └── server.js
├── scripts/
│   └── deploy-media-server.sh # Deployment script
├── terraform/
│   └── environments/oci-dev/  # Terraform configuration
└── README-DEPLOYMENT.md      # This file
```

## 🔧 Troubleshooting

### **OCI CLI Issues**
```bash
# Test OCI CLI
oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)

# Check bucket access
oci os bucket get --bucket-name sai-platform-oci-dev-media-server-repo
```

### **SSH Connection Issues**
```bash
# Test SSH connection
ssh -i ~/.ssh/oci_key opc@<server-ip> 'echo "Connection successful"'

# Check SSH key permissions
chmod 600 ~/.ssh/oci_key
```

### **Compilation Failures**
```bash
# Check server logs
ssh -i ~/.ssh/oci_key opc@<server-ip> 'sudo journalctl -u startup-script -f'

# Check mediasoup compilation
ssh -i ~/.ssh/oci_key opc@<server-ip> 'sudo tail -100 /var/log/startup-script.log'
```

### **Terraform Issues**
```bash
# Check Terraform state
cd terraform/environments/oci-dev
terraform plan

# Refresh state
terraform refresh
```

## 🎯 Best Practices

1. **Test locally first** - Make sure your code works before deploying
2. **Use --package flag** - Create compiled packages for faster future deployments  
3. **Monitor deployments** - Watch the logs to catch issues early
4. **Version your changes** - Update `package.json` version for tracking
5. **Keep backups** - Compiled packages serve as deployment backups

## 📊 Performance Comparison

| Deployment Type | Time | Use Case |
|----------------|------|----------|
| **With compiled package** | ~3 min | ✅ Production, frequent deployments |
| **Source compilation** | ~8 min | ✅ Development, first deployment |
| **Local development** | 0 min | ✅ Testing (but requires ARM64 Linux) |

## 🔮 Future Improvements

- **Automated testing** before deployment
- **Blue-green deployments** for zero downtime
- **Multi-region deployments**
- **Automated compiled package creation** on every successful deployment

---

**🎉 You now have a streamlined deployment workflow that works with your current development setup!**

No Docker required, no ARM64 Linux required - just simple, reliable deployments using OCI infrastructure.
