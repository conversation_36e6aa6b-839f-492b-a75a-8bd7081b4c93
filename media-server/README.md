# Media Server

A high-performance WebRTC-to-RTMP conversion service using mediasoup for real-time video streaming with automatic recovery and optimized streaming pipeline.

## Features

### Core Streaming
- **WebRTC video stream ingestion** with mediasoup 3.19.2
- **Real-time conversion to RTMP** using native FFmpeg 4.4.2
- **H.264 codec optimization** with forced codec selection for maximum compatibility
- **Instant streaming startup** with FFmpeg pre-initialization
- **Automatic stream recovery** from broken RTMP connections

### Performance & Reliability
- **FFmpeg pre-start architecture** for sub-second streaming initiation
- **Consumer pause/resume system** for efficient resource management
- **Automatic restart on connection failures** with graceful degradation
- **Optimized bitrate management** (5Mbps target)
- **Smart keyframe interval management** for stable video streaming

### Deployment & Infrastructure
- **ARM64 optimized** for Oracle Cloud Infrastructure
- **Ubuntu 22.04 LTS** for maximum compatibility and performance
- **Prebuilt binaries** for faster deployment (no compilation required)
- **Low memory footprint** (~28MB typical usage)
- **Configurable video encoding parameters**

## Quick Start

### Local Development

1. Install dependencies:
```bash
npm install
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Start the service:
```bash
npm start
```

The service will be available at `http://localhost:8081`

### Production Deployment (OCI ARM64)

The media server is optimized for deployment on Oracle Cloud Infrastructure ARM64 instances:

```bash
cd terraform/environments/oci-dev
terraform apply
```

This deploys:
- **Ubuntu 22.04 LTS** ARM64 instance
- **mediasoup 3.19.2** with prebuilt ARM64 binaries
- **Native FFmpeg 4.4.2** with full codec support
- **Automatic service management** with systemd
- **Firewall configuration** for WebRTC and HTTP traffic

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `8081` |
| `LISTEN_IP` | IP address to bind to | `0.0.0.0` |
| `ANNOUNCED_IP` | Public IP for WebRTC | Auto-detected |
| `MEDIASOUP_LOG_LEVEL` | Logging level | `warn` |
| `RTC_MIN_PORT` | Minimum RTC port | `32256` |
| `RTC_MAX_PORT` | Maximum RTC port | `65535` |
| `MAX_BITRATE` | Maximum video bitrate | `5000000` |
| `MAX_FRAMERATE` | Maximum framerate | `30.0` |

### Network Requirements

- **Ports**: The service requires access to the configured RTC port range (default: 32256-65535)
- **Protocols**: UDP and TCP for WebRTC communication
- **Firewall**: Ensure the port range is open for incoming connections

## Streaming Architecture

### Pre-Start Optimization

The media server uses an innovative pre-start architecture for instant streaming:

1. **Connection Phase**: Client connects → RTMP URL configured
2. **Pre-Start Phase**: Producers created → FFmpeg starts with paused consumers
3. **Streaming Phase**: User clicks "Start" → Consumers resume → Instant streaming
4. **Stop/Restart**: Consumers pause (FFmpeg keeps running) → Instant restart capability

### Automatic Recovery System

- **Broken Pipe Detection**: Monitors FFmpeg for RTMP connection failures
- **Automatic Cleanup**: Closes broken consumers and transports
- **Smart Restart**: Recreates fresh connections and resumes streaming
- **Graceful Degradation**: Continues operation even with partial failures
- **Client Notification**: Keeps UI synchronized with actual streaming state

## API Usage

### WebSocket Events

The service communicates via WebSocket using Socket.io with JWT authentication:

#### Client → Server Events

- `get-transport-info`: Request WebRTC transport information
- `connect-transport`: Connect WebRTC transport with DTLS parameters
- `create-producer`: Create a media producer (audio/video)
- `set-rtmp-url`: Configure RTMP destination URL
- `start-rtmp`: Start RTMP streaming (resumes consumers)
- `stop-rtmp`: Stop RTMP streaming (pauses consumers)
- `get-streaming-status`: Request current streaming status

#### Server → Client Events

- `transport-info`: WebRTC transport information
- `transport-connected`: Transport connection confirmation
- `producer-created`: Producer creation confirmation
- `rtmp-ready-for-producers`: Server ready for producer creation
- `rtmp-status`: RTMP streaming status updates
- `streaming-status`: Current streaming state (true/false)
- `error`: Error messages

### Example Client Integration

```javascript
import { io } from 'socket.io-client';
import { Device } from 'mediasoup-client';

// Connect with JWT authentication
const socket = io('wss://your-service-url', {
  auth: { token: 'your-jwt-token' }
});

const device = new Device();

// 1. Connect and get transport info
socket.emit('get-transport-info');
socket.on('transport-info', async (transportInfo) => {
  // Create and connect transport
  const transport = device.createSendTransport(transportInfo);
  await transport.connect({ dtlsParameters });

  // 2. Set RTMP destination
  socket.emit('set-rtmp-url', { url: 'rtmp://live.twitch.tv/app/YOUR_KEY' });

  // 3. Wait for server to request producers
  socket.on('rtmp-ready-for-producers', async () => {
    // Create producers (triggers FFmpeg pre-start)
    const videoProducer = await transport.produce({ track: videoTrack });
    const audioProducer = await transport.produce({ track: audioTrack });
  });

  // 4. Start streaming when ready
  socket.emit('start-rtmp');
});

// Monitor streaming status
socket.on('streaming-status', ({ isStreaming }) => {
  console.log('Streaming:', isStreaming);
});
```

## Monitoring

### Health Check

The service provides a health check endpoint:

```bash
curl http://localhost:8080/
```

## Troubleshooting

### Common Issues

1. **WebRTC Connection Failed**
   - Check firewall settings for RTC port range (32256-65535)
   - Verify `ANNOUNCED_IP` is set correctly for public deployments
   - Ensure JWT authentication token is valid

2. **FFmpeg/RTMP Issues**
   - **"Broken pipe" errors**: Automatically handled by recovery system
   - **H.264 codec errors**: Server forces H.264 codec selection
   - **Stream not starting**: Check RTMP URL format and credentials
   - **High latency**: Verify network connection to RTMP server

3. **Performance Issues**
   - **High CPU usage**: Adjust video encoding parameters or scale horizontally
   - **Memory leaks**: Monitor for unclosed consumers/transports
   - **Slow startup**: FFmpeg pre-start should eliminate delays

4. **Streaming Recovery**
   - **Auto-restart failures**: Check logs for specific error messages
   - **Stuck states**: Restart service to reset all connections
   - **Client sync issues**: Ensure client listens to `streaming-status` events

### Debug Mode

Enable verbose logging:

```bash
export MEDIASOUP_LOG_LEVEL=debug
export NODE_ENV=development
npm start
```

### Log Analysis

Key log patterns to monitor:

- `✅ FFmpeg pre-started` - Successful pre-initialization
- `▶️ Consumer resumed` - Streaming started
- `⏸️ Consumer paused` - Streaming stopped
- `🔄 Auto-restarting FFmpeg` - Recovery in progress
- `💥 FFmpeg detected broken RTMP connection` - Connection issue detected

## Architecture

### Data Flow

```
Client (WebGL) → WebRTC → Mediasoup → RTP → FFmpeg → RTMP → Streaming Platform
```

1. **Client**: Captures WebGL canvas as MediaStream (30fps, H.264)
2. **WebRTC**: Transmits video to mediasoup service with optimized encoding
3. **Mediasoup**: Handles WebRTC signaling, creates producers/consumers
4. **RTP**: Converts WebRTC to RTP streams for FFmpeg consumption
5. **FFmpeg**: Transcodes RTP to RTMP with codec copy (no re-encoding)
6. **RTMP**: Streams to platforms like Twitch, YouTube Live, etc.

### State Management

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Connect   │ -> │  Pre-Start   │ -> │  Streaming  │
│             │    │              │    │             │
│ • Transport │    │ • Producers  │    │ • Resume    │
│ • RTMP URL  │    │ • FFmpeg     │    │ • Keyframes │
│             │    │ • Paused     │    │ • Active    │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           │                    │
                    ┌──────────────┐    ┌─────────────┐
                    │   Recovery   │ <- │    Stop     │
                    │              │    │             │
                    │ • Cleanup    │    │ • Pause     │
                    │ • Restart    │    │ • Keep FFmpeg│
                    │ • Resume     │    │             │
                    └──────────────┘    └─────────────┘
```

### Performance Optimizations

- **FFmpeg Pre-Start**: Eliminates 3-8 second startup delay
- **Consumer Pause/Resume**: Instant stop/start without process recreation
- **Codec Copy**: No video re-encoding, preserves quality and reduces CPU
- **Smart Keyframes**: Optimized keyframe intervals for stable streaming
- **Connection Pooling**: Reuses WebRTC connections for multiple sessions

## License

MIT License
