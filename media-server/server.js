// Load environment variables
const dotenvResult = require('dotenv').config();
console.log('🔧 Dotenv result:', dotenvResult.error ? `Error: ${dotenvResult.error}` : 'Success');
console.log('🔧 All environment variables:', {
  JWT_SECRET: process.env.JWT_SECRET ? `${process.env.JWT_SECRET.substring(0, 15)}...` : 'NOT SET',
  PORT: process.env.PORT,
  NODE_ENV: process.env.NODE_ENV
});

const express = require('express');
const http = require('http');
const https = require('https');
const fs = require('fs');
const socketIo = require('socket.io');
const cors = require('cors');
const mediasoup = require('mediasoup');
const { spawn } = require('child_process');
const jwt = require('jsonwebtoken');

const CONFIG = process.env.NODE_ENV === 'production'
  ? require('./config.production')
  : require('./config');

const app = express();

// Create server (HTTP or HTTPS based on environment)
let server;
if (process.env.ENABLE_HTTPS === 'true' && process.env.SSL_CERT_PATH && process.env.SSL_KEY_PATH) {
  console.log('🔒 Starting HTTPS server');
  const options = {
    cert: fs.readFileSync(process.env.SSL_CERT_PATH),
    key: fs.readFileSync(process.env.SSL_KEY_PATH)
  };
  server = https.createServer(options, app);
} else {
  console.log('🌐 Starting HTTP server');
  server = http.createServer(app);
}

const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Apply authentication middleware
io.use(authenticateSocket);

app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    status: 'ok',
    service: 'media-server',
    timestamp: new Date().toISOString(),
    connections: io.engine.clientsCount
  });
});

// Global mediasoup objects - simplified for direct connections
let worker;
let router;
let webRtcTransport;
let producer;
let audioProducer;
let rtpTransport;
let audioRtpTransport;
let consumer;
let audioConsumer;
let ffmpegProcess;
let keyframeInterval;
let statsInterval;
let audioStatsInterval;
let rtmpUrl = null; // Store RTMP URL provided by frontend
let rtmpUrlSetTime = null; // Track when RTMP URL was set for minimum delay

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
console.log('🔑 JWT_SECRET loaded:', JWT_SECRET ? `${JWT_SECRET.substring(0, 10)}...` : 'NOT SET');

// Store authenticated user sessions
const authenticatedSockets = new Map(); // socketId -> { userId, email, name }

// JWT verification function
function verifyJWT(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    return {
      userId: decoded.sub,
      email: decoded.email,
      name: decoded.name
    };
  } catch (error) {
    console.error('❌ JWT verification failed:', error.message);
    console.error('🔍 Token preview:', token.substring(0, 50) + '...');
    return null;
  }
}

// Authentication middleware for socket connections
function authenticateSocket(socket, next) {
  try {
    // Extract token from handshake
    const token = socket.handshake.auth?.token ||
                  socket.handshake.headers?.authorization?.replace('Bearer ', '');

    if (!token) {
      console.warn(`🔒 Socket ${socket.id} connected without token`);
      return next(new Error('Authentication required'));
    }

    // Verify JWT token
    const user = verifyJWT(token);
    if (!user) {
      console.warn(`🔒 Socket ${socket.id} authentication failed`);
      return next(new Error('Invalid token'));
    }

    // Store user info for this socket
    authenticatedSockets.set(socket.id, user);
    console.log(`✅ Authenticated user ${user.userId} (${user.email}) connected: ${socket.id}`);

    next();
  } catch (error) {
    console.error(`❌ Authentication error for socket ${socket.id}:`, error.message);
    next(new Error('Authentication failed'));
  }
}

// Initialize mediasoup
async function initializeMediasoup() {
  try {
    // Create worker
    worker = await mediasoup.createWorker(CONFIG.mediasoup.worker);
    console.log('✅ Mediasoup worker created [pid:%d]', worker.pid);

    worker.on('died', () => {
      console.error('❌ Mediasoup worker died, exiting...');
      process.exit(1);
    });

    // Create router with both audio and video codecs for RTMP compatibility
    router = await worker.createRouter(CONFIG.mediasoup.router);
    console.log('✅ Mediasoup router created');

    return true;
  } catch (error) {
    console.error('❌ Failed to initialize mediasoup:', error);
    return false;
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  const user = authenticatedSockets.get(socket.id);
  console.log('🔌 Authenticated client connected:', socket.id, `(User: ${user.userId})`);
  console.log('📊 Total connections:', io.engine.clientsCount);

  // Handle WebRTC transport creation
  socket.on('get-transport-info', async () => {
    console.log('📡 Client requested transport info');
    try {
      if (!router) {
        console.error('❌ Router not initialized');
        socket.emit('error', { message: 'Router not initialized' });
        return;
      }

      // Create WebRTC transport
      webRtcTransport = await router.createWebRtcTransport(CONFIG.mediasoup.webrtcTransport);

      const transportInfo = {
        id: webRtcTransport.id,
        iceParameters: webRtcTransport.iceParameters,
        iceCandidates: webRtcTransport.iceCandidates,
        dtlsParameters: webRtcTransport.dtlsParameters,
        routerRtpCapabilities: router.rtpCapabilities,
      };

      socket.emit('transport-info', transportInfo);
      console.log('✅ WebRTC transport created and sent to client');

    } catch (error) {
      console.error('❌ Failed to create transport:', error);
      socket.emit('error', { message: 'Failed to create transport' });
    }
  });

  // Handle transport connection
  socket.on('connect-transport', async ({ dtlsParameters }) => {
    console.log('🔗 Client connecting transport');
    try {
      if (!webRtcTransport) {
        console.error('❌ Transport not created');
        socket.emit('error', { message: 'Transport not created' });
        return;
      }

      await webRtcTransport.connect({ dtlsParameters });
      socket.emit('transport-connected');
      console.log('✅ WebRTC transport connected');
    } catch (error) {
      console.error('❌ Failed to connect transport:', error);
      socket.emit('error', { message: 'Failed to connect transport' });
    }
  });

  // Handle producer creation
  socket.on('create-producer', async ({ kind, rtpParameters }) => {
    console.log('🎬 Client creating producer, kind:', kind);
    try {
      if (!webRtcTransport) {
        console.error('❌ Transport not connected');
        socket.emit('error', { message: 'Transport not connected' });
        return;
      }

      // Clean up existing producer of the same kind first
      if (kind === 'video' && producer) {
        console.log('🧹 Cleaning up existing video producer:', producer.id);
        producer.close();
        producer = null;
      } else if (kind === 'audio' && audioProducer) {
        console.log('🧹 Cleaning up existing audio producer:', audioProducer.id);
        audioProducer.close();
        audioProducer = null;
      }

      const newProducer = await webRtcTransport.produce({ kind, rtpParameters });

      // Store producers by kind
      if (kind === 'video') {
        producer = newProducer;
        console.log('✅ Video producer created:', producer.id);
      } else if (kind === 'audio') {
        audioProducer = newProducer;
        console.log('✅ Audio producer created:', audioProducer.id);
      }

      socket.emit('producer-created', { producerId: newProducer.id });
      // Producer created successfully

      // Monitor producer stats
      if (kind === 'video') {
        // Clear existing video stats interval if any
        if (statsInterval) {
          clearInterval(statsInterval);
        }

        statsInterval = setInterval(async () => {
          try {
            if (producer && !producer.closed) {
              const stats = await producer.getStats();
              const mainStat = stats[0];
              if (mainStat) {
                console.log(`📊 Video: ${Math.round(mainStat.bitrate/1000)}kbps, ${mainStat.packetCount} packets, ${mainStat.packetsLost} lost`);
              }
            }
          } catch (error) {
            console.log('⚠️ Failed to get video producer stats:', error);
            if (statsInterval) {
              clearInterval(statsInterval);
              statsInterval = null;
            }
          }
        }, 10000);
      } else if (kind === 'audio') {
        // Clear existing audio stats interval if any
        if (audioStatsInterval) {
          clearInterval(audioStatsInterval);
        }

        audioStatsInterval = setInterval(async () => {
          try {
            if (audioProducer && !audioProducer.closed) {
              const stats = await audioProducer.getStats();
              const mainStat = stats[0];
              if (mainStat) {
                console.log(`📊 Audio: ${Math.round(mainStat.bitrate/1000)}kbps, ${mainStat.packetCount} packets, ${mainStat.packetsLost} lost (input Opus - FFmpeg outputs 128k AAC)`);
              }
            }
          } catch (error) {
            console.log('⚠️ Failed to get audio producer stats:', error);
            if (audioStatsInterval) {
              clearInterval(audioStatsInterval);
              audioStatsInterval = null;
            }
          }
        }, 10000);
      }

      newProducer.on('close', () => {
        if (kind === 'video' && statsInterval) {
          clearInterval(statsInterval);
          statsInterval = null;
        } else if (kind === 'audio' && audioStatsInterval) {
          clearInterval(audioStatsInterval);
          audioStatsInterval = null;
        }
        console.log('🔌 Producer closed:', kind);
      });

      // Auto-start FFmpeg when video producer is created (if RTMP URL is configured)
      if (kind === 'video') {
        console.log('🎬 Video producer created, FFmpeg ready:', !!ffmpegProcess);

        if (rtmpUrl) {
          console.log('🔥 Video producer created, pre-starting FFmpeg with paused consumers...');
          try {
            await startRTMPStreaming();
            console.log('✅ FFmpeg pre-started with paused consumers - ready for streaming');
            socket.emit('ffmpeg-ready', { message: 'FFmpeg initialized and ready for streaming' });
          } catch (error) {
            console.error('❌ Failed to pre-start FFmpeg:', error);
            socket.emit('error', { message: 'Failed to initialize streaming: ' + error.message });
          }
        } else {
          console.log('⚠️ Video producer created but RTMP URL not configured.');
          socket.emit('rtmp-url-required', { message: 'RTMP URL required to start streaming' });
        }
      }
    } catch (error) {
      console.error('❌ Failed to create producer:', error);
      socket.emit('error', { message: 'Failed to create producer' });
    }
  });

  // Handle RTMP URL configuration
  socket.on('set-rtmp-url', async ({ url }) => {
    const user = authenticatedSockets.get(socket.id);
    if (!user) {
      console.warn('🔒 Unauthorized attempt to set RTMP URL');
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    console.log(`🔧 User ${user.userId} (${user.email}) setting RTMP URL:`, url);
    rtmpUrl = url;
    rtmpUrlSetTime = Date.now(); // Track when RTMP URL was set
    socket.emit('rtmp-url-set', { success: true, url });
    console.log('✅ RTMP URL configured:', url);

    // Signal client that RTMP URL is set and they should create producers
    console.log('📡 RTMP URL configured, requesting client to create producers for pre-start...');
    socket.emit('rtmp-ready-for-producers', { message: 'RTMP URL configured, please create producers' });

    // Also emit to all connected clients in case the original client missed it
    socket.broadcast.emit('rtmp-ready-for-producers', { message: 'RTMP URL configured, please create producers' });
  });

  // Handle manual start RTMP request
  socket.on('start-rtmp', async () => {
    const user = authenticatedSockets.get(socket.id);
    if (!user) {
      console.warn('🔒 Unauthorized attempt to start RTMP streaming');
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    console.log(`🚀 User ${user.userId} (${user.email}) requested to start RTMP streaming`);
    try {
      if (!rtmpUrl) {
        socket.emit('error', { message: 'RTMP URL not configured. Please set RTMP URL first.' });
        return;
      }

      if (!producer) {
        socket.emit('error', { message: 'No video producer available. Please start video first.' });
        return;
      }

      if (!ffmpegProcess || !consumer || !audioConsumer) {
        console.log('⚠️ FFmpeg or consumers not available, starting fresh...');
        await startRTMPStreaming();
      } else {
        // Ensure minimum 3-second delay from RTMP URL setting
        if (rtmpUrlSetTime) {
          const timeSinceRtmpSet = Date.now() - rtmpUrlSetTime;
          const minimumDelay = 3000; // 3 seconds

          if (timeSinceRtmpSet < minimumDelay) {
            const remainingDelay = minimumDelay - timeSinceRtmpSet;
            console.log(`⏳ Waiting ${remainingDelay}ms for FFmpeg initialization (minimum 3-second delay)`);
            await new Promise(resolve => setTimeout(resolve, remainingDelay));
          } else {
            console.log(`✅ FFmpeg has had ${Math.round(timeSinceRtmpSet/1000)}s to initialize, proceeding immediately`);
          }
        }

        console.log('✅ FFmpeg already running, checking consumer validity before resuming...');

        // Check if consumers are still valid (not closed due to connection issues)
        const audioValid = audioConsumer && !audioConsumer.closed;
        const videoValid = consumer && !consumer.closed;

        if (!audioValid && !videoValid) {
          console.log('⚠️ All consumers are closed (likely due to connection issue), recreating...');
          // Clear references and recreate
          audioConsumer = null;
          consumer = null;
          // Restart the streaming process to recreate consumers
          return startRTMPStreaming(user.userId, useAudio, useVideo);
        }

        // Resume valid consumers
        if (audioValid && audioConsumer.paused) {
          try {
            await audioConsumer.resume();
            console.log('▶️ Audio consumer resumed - streaming started');
          } catch (error) {
            console.log('❌ Failed to resume audio consumer:', error.message);
            audioConsumer = null; // Clear invalid reference
          }
        }

        if (videoValid && consumer.paused) {
          try {
            await consumer.resume();
            console.log('▶️ Video consumer resumed - streaming started');
            // Start keyframe interval for video
            startKeyframeInterval();
          } catch (error) {
            console.log('❌ Failed to resume video consumer:', error.message);
            consumer = null; // Clear invalid reference
          }
        }
      }

      // Streaming status is based on whether consumers are active (not paused)
      const isStreaming = (consumer && !consumer.paused) || (audioConsumer && !audioConsumer.paused);
      socket.emit('rtmp-status', {
        streaming: isStreaming,
        message: isStreaming ? 'RTMP stream started' : 'Failed to start RTMP stream'
      });
      socket.emit('streaming-status', { isStreaming });
      console.log(`📡 RTMP streaming started by user ${user.userId}, sent status:`, isStreaming);
    } catch (error) {
      console.error('❌ Failed to start RTMP streaming:', error);
      socket.emit('error', { message: 'Failed to start RTMP streaming: ' + error.message });
    }
  });

  // Handle streaming status request
  socket.on('get-streaming-status', () => {
    console.log('📊 Client requested streaming status');
    // Streaming status is based on whether consumers are active (not paused)
    const isStreaming = (consumer && !consumer.paused) || (audioConsumer && !audioConsumer.paused);
    socket.emit('streaming-status', { isStreaming });
    console.log('📡 Sent streaming status:', isStreaming);
  });

  // Handle stop RTMP request
  socket.on('stop-rtmp', async () => {
    const user = authenticatedSockets.get(socket.id);
    if (!user) {
      console.warn('🔒 Unauthorized attempt to stop RTMP streaming');
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    console.log(`⏹️ User ${user.userId} (${user.email}) requested to stop RTMP streaming`);
    try {
      await stopRTMPStreaming();
      // Streaming status is based on whether consumers are active (not paused)
      const isStreaming = (consumer && !consumer.paused) || (audioConsumer && !audioConsumer.paused);
      socket.emit('rtmp-status', {
        streaming: isStreaming,
        message: isStreaming ? 'Failed to stop RTMP stream' : 'RTMP stream stopped'
      });
      socket.emit('streaming-status', { isStreaming });
      console.log(`📡 RTMP streaming stopped by user ${user.userId}, sent status:`, isStreaming);
    } catch (error) {
      console.error('❌ Failed to stop RTMP streaming:', error);
      socket.emit('error', { message: 'Failed to stop RTMP streaming: ' + error.message });
    }
  });

  socket.on('disconnect', (reason) => {
    const user = authenticatedSockets.get(socket.id);
    console.log(`🔌 Client disconnected: ${socket.id} (User: ${user ? user.userId : 'Unknown'}) Reason:`, reason);
    console.log('📊 Total connections remaining:', io.engine.clientsCount);

    // Clean up authenticated session
    if (user) {
      authenticatedSockets.delete(socket.id);
      console.log(`🔒 Cleaned up session for user ${user.userId}`);
    }

    // Perform cleanup to stop streaming and free resources
    cleanup();

    console.log('✅ Cleanup completed for disconnected client');
  });
});

// RTMP Streaming
async function startRTMPStreaming() {
  console.log('🚀 startRTMPStreaming() called');
  try {
    if (!producer || !router) {
      console.log('❌ Missing dependencies - producer:', !!producer, 'router:', !!router);
      throw new Error('Producer or router not available');
    }
    console.log('✅ Dependencies available, proceeding with RTP transport creation...');

    const useAudio = audioProducer !== null && audioProducer !== undefined;
    const useVideo = producer !== null && producer !== undefined;

    console.log('🎵 Audio available:', useAudio, 'Video available:', useVideo);

    // Create audio RTP transport and consumer if audio producer exists and not already created
    if (useAudio && !audioConsumer) {
      audioRtpTransport = await router.createPlainTransport({
        comedia: false,
        rtcpMux: false,
        ...CONFIG.mediasoup.plainTransport,
      });

      await audioRtpTransport.connect({
        ip: CONFIG.mediasoup.recording.ip,
        port: CONFIG.mediasoup.recording.audioPort,
        rtcpPort: CONFIG.mediasoup.recording.audioPortRtcp,
      });

      console.log('✅ Audio RTP transport connected:',
        `${audioRtpTransport.tuple.localIp}:${audioRtpTransport.tuple.localPort} <--> ${audioRtpTransport.tuple.remoteIp}:${audioRtpTransport.tuple.remotePort}`);

      audioConsumer = await audioRtpTransport.consume({
        producerId: audioProducer.id,
        rtpCapabilities: router.rtpCapabilities,
        paused: true,
      });

      console.log('✅ Audio consumer created:', audioConsumer.kind, audioConsumer.type, 'paused:', audioConsumer.paused);
    } else if (useAudio && audioConsumer) {
      console.log('✅ Audio consumer already exists, reusing');
    } else {
      console.log('⚠️ No audio producer available, proceeding with video only');
    }

    // Create video RTP transport and consumer if not already created
    if (useVideo && !consumer) {
      rtpTransport = await router.createPlainTransport({
        comedia: false,
        rtcpMux: false,
        ...CONFIG.mediasoup.plainTransport,
      });

      await rtpTransport.connect({
        ip: CONFIG.mediasoup.recording.ip,
        port: CONFIG.mediasoup.recording.videoPort,
        rtcpPort: CONFIG.mediasoup.recording.videoPortRtcp,
      });

      console.log('✅ Video RTP transport connected:',
        `${rtpTransport.tuple.localIp}:${rtpTransport.tuple.localPort} <--> ${rtpTransport.tuple.remoteIp}:${rtpTransport.tuple.remotePort}`);

      consumer = await rtpTransport.consume({
        producerId: producer.id,
        rtpCapabilities: router.rtpCapabilities,
        paused: true,
      });

      console.log('✅ Video consumer created:', consumer.kind, consumer.type, 'paused:', consumer.paused);
    } else if (useVideo && consumer) {
      console.log('✅ Video consumer already exists, reusing');
    }

    // FFmpeg should already be running from pre-start, just resume consumers
    if (!ffmpegProcess) {
      console.log('⚠️ FFmpeg not pre-started, starting now...');
      try {
        await Promise.race([
          startFFmpeg(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('FFmpeg start timeout')), 8000))
        ]);
        console.log('✅ FFmpeg started successfully');
      } catch (error) {
        if (error.message === 'FFmpeg start timeout') {
          console.log('⚠️ FFmpeg start timed out, but process may be running - continuing...');
        } else {
          throw error;
        }
      }
      // Wait a moment for FFmpeg to initialize
      await new Promise(resolve => setTimeout(resolve, 1000));
    } else {
      console.log('✅ Using pre-started FFmpeg process');
    }

    // Keep consumers paused for pre-start - they will be resumed when user clicks "Start Stream"
    console.log('✅ Consumers created and paused - ready for streaming when user clicks Start');
    console.log('📊 Audio consumer paused:', audioConsumer?.paused);
    console.log('📊 Video consumer paused:', consumer?.paused);

    console.log('✅ RTMP streaming started with audio:', useAudio, 'video:', useVideo);

  } catch (error) {
    console.error('❌ Failed to start RTMP streaming:', error);
  }
}



// Keyframe interval management
function startKeyframeInterval() {
  if (keyframeInterval) {
    clearInterval(keyframeInterval);
  }

  keyframeInterval = setInterval(() => {
    if (consumer && !consumer.closed) {
      // Request keyframe from the consumer (not producer)
      consumer.requestKeyFrame()
        .then(() => console.log('🔑 Keyframe requested'))
        .catch(err => console.log('⚠️ Keyframe request failed:', err));
    }
  }, 2000); // Every 2 seconds

  console.log('🔑 Keyframe interval started');
}


// FFmpeg RTMP streaming
async function startFFmpeg() {
  return new Promise((resolve, reject) => {
    // Intelligently choose FFmpeg path: system FFmpeg (ARM64) or ffmpeg-static (local dev)
    let ffmpegPath;
    try {
      // Try system FFmpeg first (preferred for ARM64 deployment)
      const { execSync } = require('child_process');
      execSync('which ffmpeg', { stdio: 'ignore' });
      ffmpegPath = 'ffmpeg';
      console.log('🎬 Using system FFmpeg (ARM64 compatible)');
    } catch (error) {
      // Fallback to ffmpeg-static for local development
      try {
        ffmpegPath = require('ffmpeg-static');
        console.log('🎬 Using ffmpeg-static (local development)');
      } catch (staticError) {
        reject(new Error('FFmpeg not available: neither system FFmpeg nor ffmpeg-static found'));
        return;
      }
    }

    // Check if RTMP URL is provided by frontend
    if (!rtmpUrl) {
      const error = new Error('RTMP URL not configured. Please set RTMP URL before starting stream.');
      console.error('❌', error.message);
      reject(error);
      return;
    }

    console.log('🎯 Using RTMP URL provided by frontend:', rtmpUrl);

    // Build FFmpeg arguments
    const useAudio = audioProducer !== null;
    const useVideo = producer !== null;

    let args = [
      '-nostdin',
      '-protocol_whitelist', 'file,rtp,udp',
      '-fflags', '+genpts',
      '-v', 'verbose',  // Add verbose logging
      '-i', `${__dirname}/recording/input-h264.sdp`
    ];

    // Add codec mappings with constant bitrate for YouTube
    if (useAudio) {
      args.push('-map', '0:a:0', '-c:a', 'aac', '-b:a', '128k', '-minrate:a', '128k', '-maxrate:a', '128k', '-ar', '48000', '-ac', '2', '-profile:a', 'aac_low', '-af', 'volume=1.0');
    }
    if (useVideo) {
      args.push('-map', '0:v:0', '-c:v', 'copy');
    }

    // Add output format and destination
    args.push('-f', 'flv', '-rtmp_live', 'live', rtmpUrl);

    console.log('🎬 Starting FFmpeg with audio:', useAudio, 'video:', useVideo);
    console.log('🎬 FFmpeg command:', ffmpegPath, args.join(' '));

    ffmpegProcess = spawn(ffmpegPath, args);

    ffmpegProcess.on('error', (err) => {
      console.error('❌ FFmpeg error:', err);
      reject(err);
    });


    ffmpegProcess.on('exit', (code, signal) => {
      console.log('🎬 FFmpeg exited with code:', code, 'signal:', signal);
      const wasStreaming = (consumer && !consumer.paused) || (audioConsumer && !audioConsumer.paused);
      ffmpegProcess = null;

      // If FFmpeg exits unexpectedly (not due to SIGINT), it might be due to broken pipe
      if (code !== 0 && signal !== 'SIGINT') {
        console.log('⚠️ FFmpeg exited unexpectedly, likely due to broken RTMP connection');
        console.log('🧹 Cleaning up consumers and transports for fresh restart');

        // Clear consumers so they can be recreated on next start
        if (consumer) {
          consumer.close();
          consumer = null;
        }
        if (audioConsumer) {
          audioConsumer.close();
          audioConsumer = null;
        }
        if (rtpTransport) {
          rtpTransport.close();
          rtpTransport = null;
        }
        if (audioRtpTransport) {
          audioRtpTransport.close();
          audioRtpTransport = null;
        }

        console.log('✅ Cleanup completed, ready for fresh FFmpeg start');

        // Auto-restart if we were actively streaming
        if (wasStreaming && producer && rtmpUrl) {
          console.log('🔄 Auto-restarting FFmpeg since we were actively streaming...');
          setTimeout(async () => {
            try {
              // Start FFmpeg with paused consumers (like normal pre-start)
              console.log('🔄 Starting FFmpeg auto-restart...');
              await Promise.race([
                startRTMPStreaming(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Auto-restart timeout')), 10000))
              ]);
              console.log('✅ FFmpeg auto-restart successful with paused consumers');

              // Add a small delay to ensure consumers are fully ready
              await new Promise(resolve => setTimeout(resolve, 500));

              // Debug consumer states
              console.log('🔍 Consumer states before resume:', {
                audioConsumer: audioConsumer ? { paused: audioConsumer.paused, closed: audioConsumer.closed } : 'null',
                videoConsumer: consumer ? { paused: consumer.paused, closed: consumer.closed } : 'null'
              });

              // Now resume consumers to continue streaming
              if (audioConsumer && audioConsumer.paused) {
                await audioConsumer.resume();
                console.log('▶️ Audio consumer resumed after auto-restart');
              } else if (audioConsumer) {
                console.log('⚠️ Audio consumer not paused, current state:', audioConsumer.paused);
              }

              if (consumer && consumer.paused) {
                await consumer.resume();
                console.log('▶️ Video consumer resumed after auto-restart');

                // Restart keyframe interval
                startKeyframeInterval();
              } else if (consumer) {
                console.log('⚠️ Video consumer not paused, current state:', consumer.paused);
                // Start keyframes anyway since we're streaming
                startKeyframeInterval();
              }

              // Notify all connected clients about successful restart
              const isStreaming = (consumer && !consumer.paused) || (audioConsumer && !audioConsumer.paused);
              io.emit('streaming-status', { isStreaming });
              io.emit('rtmp-status', {
                streaming: isStreaming,
                message: 'Stream automatically restarted after connection issue'
              });
              console.log('✅ Auto-restart completed, streaming resumed');
            } catch (error) {
              console.error('❌ FFmpeg auto-restart failed:', error);

              // Notify clients about the failure
              io.emit('streaming-status', { isStreaming: false });
              io.emit('rtmp-status', {
                streaming: false,
                message: 'Stream restart failed: ' + error.message
              });
            }
          }, 2000); // 2 second delay to let things settle
        }
      }
    });

    let inputStreamDetected = false;
    let streamMappingDetected = false;
    let ffmpegFullyReady = false;

    ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log('FFmpeg:', output);

      // Track FFmpeg readiness stages
      if (output.includes('Input #0') && !inputStreamDetected) {
        console.log('📊 FFmpeg detected input stream');
        inputStreamDetected = true;
      }

      if (output.includes('Stream mapping:') && !streamMappingDetected) {
        console.log('📊 FFmpeg stream mapping established');
        streamMappingDetected = true;
      }

      // Reduce frame processing log frequency
      if (output.includes('frame=') && Math.random() < 0.1) {
        console.log('📊 FFmpeg processing frames');
      }

      if (output.includes('Connection to tcp://')) {
        console.log('📊 FFmpeg connecting to RTMP server');
      }

      if (output.includes('Stream publish started')) {
        console.log('📊 FFmpeg RTMP publish started');
      }

      // Detect broken pipe errors
      if (output.includes('Broken pipe') || output.includes('Connection reset by peer')) {
        console.log('💥 FFmpeg detected broken RTMP connection, will restart on next stream attempt');
      }

      // FFmpeg is fully ready when we have both input detection AND stream mapping
      if (inputStreamDetected && streamMappingDetected && !ffmpegFullyReady) {
        ffmpegFullyReady = true;
        console.log('✅ FFmpeg fully ready - both input and mapping detected');
        resolve();
      }
      // Fallback: resolve on first frame processing if mapping wasn't detected
      else if (output.includes('frame=') && !ffmpegFullyReady) {
        ffmpegFullyReady = true;
        console.log('✅ FFmpeg ready - frame processing started');
        resolve();
      }
    });

    ffmpegProcess.stdout.on('data', (data) => {
      console.log('FFmpeg stdout:', data.toString());
    });

    // Create SDP file for FFmpeg input
    createSDPFile();
  });
}

function createSDPFile() {
  const useAudio = audioProducer !== null && audioProducer !== undefined;
  const useVideo = producer !== null && producer !== undefined;

  // Create SDP file based on available producers
  let sdpContent = `v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
c=IN IP4 127.0.0.1
t=0 0
`;

  if (useAudio) {
    sdpContent += `m=audio ${CONFIG.mediasoup.recording.audioPort} RTP/AVPF 111
a=rtcp:${CONFIG.mediasoup.recording.audioPortRtcp}
a=rtpmap:111 opus/48000/2
a=fmtp:111 minptime=10;useinbandfec=1
`;
  }

  if (useVideo) {
    sdpContent += `m=video ${CONFIG.mediasoup.recording.videoPort} RTP/AVPF 125
a=rtcp:${CONFIG.mediasoup.recording.videoPortRtcp}
a=rtpmap:125 H264/90000
a=fmtp:125 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d0032
a=framerate:30
b=AS:5000
`;
  }

  const sdpPath = `${__dirname}/recording/input-h264.sdp`;
  require('fs').writeFileSync(sdpPath, sdpContent);
  console.log('✅ SDP file created with audio:', useAudio, 'video:', useVideo);
}

// Stop RTMP streaming (pause consumers, keep FFmpeg running for fast restart)
async function stopRTMPStreaming() {
  console.log('⏸️ stopRTMPStreaming() called - pausing consumers only');
  try {
    if (keyframeInterval) {
      console.log('⏸️ Stopping keyframe interval');
      clearInterval(keyframeInterval);
      keyframeInterval = null;
    }

    // Pause consumers to stop data flow, but keep FFmpeg running
    if (consumer && !consumer.paused) {
      console.log('⏸️ Pausing video consumer');
      await consumer.pause();
    }

    if (audioConsumer && !audioConsumer.paused) {
      console.log('⏸️ Pausing audio consumer');
      await audioConsumer.pause();
    }

    // Keep FFmpeg, RTP transports, and consumers alive for instant restart
    console.log('✅ Streaming paused - FFmpeg still running for fast restart');
  } catch (error) {
    console.error('❌ Failed to pause streaming:', error);
    throw error;
  }
}

// Cleanup function (called on disconnect - kills FFmpeg and closes all resources)
function cleanup() {
  console.log('🧹 Starting cleanup process on disconnect...');

  // Kill FFmpeg process on disconnect
  if (ffmpegProcess) {
    console.log('⏹️ Killing FFmpeg process on disconnect');
    ffmpegProcess.kill('SIGINT');
    ffmpegProcess = null;
  }

  // Clear all intervals
  if (keyframeInterval) {
    console.log('⏹️ Clearing keyframe interval');
    clearInterval(keyframeInterval);
    keyframeInterval = null;
  }

  if (statsInterval) {
    console.log('⏹️ Clearing video stats interval');
    clearInterval(statsInterval);
    statsInterval = null;
  }

  if (audioStatsInterval) {
    console.log('⏹️ Clearing audio stats interval');
    clearInterval(audioStatsInterval);
    audioStatsInterval = null;
  }

  // Close consumers
  if (consumer) {
    console.log('⏹️ Closing video consumer');
    consumer.close();
    consumer = null;
  }

  if (audioConsumer) {
    console.log('⏹️ Closing audio consumer');
    audioConsumer.close();
    audioConsumer = null;
  }

  // Close producers
  if (audioProducer) {
    console.log('⏹️ Closing audio producer');
    audioProducer.close();
    audioProducer = null;
  }

  if (producer) {
    console.log('⏹️ Closing video producer');
    producer.close();
    producer = null;
  }

  // Close transports
  if (rtpTransport) {
    console.log('⏹️ Closing RTP transport');
    rtpTransport.close();
    rtpTransport = null;
  }

  if (audioRtpTransport) {
    console.log('⏹️ Closing audio RTP transport');
    audioRtpTransport.close();
    audioRtpTransport = null;
  }

  if (webRtcTransport) {
    console.log('⏹️ Closing WebRTC transport');
    webRtcTransport.close();
    webRtcTransport = null;
  }

  console.log('✅ Cleanup completed');
}

// Create recording directory
const recordingDir = `${__dirname}/recording`;
if (!fs.existsSync(recordingDir)) {
  fs.mkdirSync(recordingDir, { recursive: true });
}

// Start server
async function startServer() {
  try {
    const initialized = await initializeMediasoup();
    if (!initialized) {
      process.exit(1);
    }

    const PORT = process.env.MEDIA_SERVER_PORT || process.env.PORT || 8080;
    const HOST = process.env.LISTEN_IP || '0.0.0.0';

    server.listen(PORT, HOST, () => {
      console.log(`🚀 Media Server running on ${HOST}:${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log(`🌐 External access: https://${process.env.ANNOUNCED_IP || HOST}:${PORT}/`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

