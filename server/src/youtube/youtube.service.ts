import { Injectable, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { google, youtube_v3 } from 'googleapis';
import { UsersService } from '../users/users.service';

export interface YouTubeBroadcast {
  id: string;
  title: string;
  description: string;
  scheduledStartTime: string;
  status: string;
  streamId?: string;
  streamKey?: string;
  rtmpUrl?: string;
}

export interface YouTubeStream {
  id: string;
  title: string;
  description: string;
  streamKey: string;
  rtmpUrl: string;
  status: string;
}

export interface YouTubeChannel {
  id: string;
  title: string;
  description: string;
  thumbnailUrl?: string;
  customUrl?: string;
}

@Injectable()
export class YouTubeService {
  constructor(private usersService: UsersService) {}

  private async getYouTubeClient(userId: string): Promise<youtube_v3.Youtube> {
    const user = await this.usersService.findById(userId);

    // Find Google platform in linkedPlatforms
    const googlePlatform = user.linkedPlatforms.find(p => p.provider === 'google');
    if (!googlePlatform || !googlePlatform.accessToken) {
      throw new UnauthorizedException('User has not authorized YouTube access');
    }

    // Check if token is expired and refresh if needed
    if (googlePlatform.tokenExpiresAt && googlePlatform.tokenExpiresAt < new Date()) {
      if (!googlePlatform.refreshToken) {
        throw new UnauthorizedException('Access token expired and no refresh token available');
      }
      // TODO: Implement token refresh logic
      throw new UnauthorizedException('Access token expired. Please re-authenticate.');
    }

    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({
      access_token: googlePlatform.accessToken,
      refresh_token: googlePlatform.refreshToken,
    });

    return google.youtube({ version: 'v3', auth: oauth2Client });
  }

  async createBroadcast(
    userId: string,
    title: string,
    description: string,
    scheduledStartTime?: Date,
    privacy: 'public' | 'unlisted' | 'private' = 'unlisted'
  ): Promise<YouTubeBroadcast> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      const broadcast = await youtube.liveBroadcasts.insert({
        part: ['snippet', 'status'],
        requestBody: {
          snippet: {
            title,
            description,
            scheduledStartTime: scheduledStartTime?.toISOString() || new Date().toISOString(),
          },
          status: {
            privacyStatus: privacy,
            selfDeclaredMadeForKids: false,
          },
        },
      });

      return {
        id: broadcast.data.id!,
        title: broadcast.data.snippet!.title!,
        description: broadcast.data.snippet!.description || '',
        scheduledStartTime: broadcast.data.snippet!.scheduledStartTime!,
        status: broadcast.data.status!.lifeCycleStatus!,
      };
    } catch (error) {
      console.error('Error creating YouTube broadcast:', error);
      throw new BadRequestException('Failed to create YouTube broadcast');
    }
  }

  async createStream(
    userId: string,
    title: string,
    description: string,
    resolution: '240p' | '360p' | '480p' | '720p' | '1080p' = '720p'
  ): Promise<YouTubeStream> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      const stream = await youtube.liveStreams.insert({
        part: ['snippet', 'cdn'],
        requestBody: {
          snippet: {
            title,
            description,
          },
          cdn: {
            frameRate: '30fps',
            ingestionType: 'rtmp',
            resolution,
          },
        },
      });

      console.log('YouTube stream created successfully:', {
        id: stream.data.id,
        title: stream.data.snippet?.title,
        hasIngestionInfo: !!stream.data.cdn?.ingestionInfo,
        status: stream.data.status
      });

      return {
        id: stream.data.id!,
        title: stream.data.snippet!.title!,
        description: stream.data.snippet!.description || '',
        streamKey: stream.data.cdn!.ingestionInfo!.streamName!,
        rtmpUrl: stream.data.cdn!.ingestionInfo!.ingestionAddress!,
        status: stream.data.status?.streamStatus || 'created',
      };
    } catch (error) {
      console.error('Error creating YouTube stream:', error);
      console.error('Request body was:', JSON.stringify({
        snippet: { title, description },
        cdn: { frameRate: '30fps', ingestionType: 'rtmp', resolution }
      }, null, 2));

      // Log the full response if available
      if (error?.response?.data) {
        console.error('YouTube API response:', JSON.stringify(error.response.data, null, 2));
      }

      // Extract more specific error information
      const errorMessage = error?.response?.data?.error?.message || error?.message || 'Failed to create YouTube stream';
      throw new BadRequestException(`YouTube stream creation failed: ${errorMessage}`);
    }
  }

  async bindBroadcastToStream(
    userId: string,
    broadcastId: string,
    streamId: string
  ): Promise<YouTubeBroadcast> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      const result = await youtube.liveBroadcasts.bind({
        part: ['snippet', 'status'],
        id: broadcastId,
        streamId: streamId,
      });

      // Get the stream details to include RTMP info
      const streamDetails = await youtube.liveStreams.list({
        part: ['cdn'],
        id: [streamId],
      });

      const stream = streamDetails.data.items?.[0];

      console.log('🔗 Binding broadcast to stream:', {
        broadcastId,
        streamId,
        hasStream: !!stream,
        hasIngestionInfo: !!stream?.cdn?.ingestionInfo,
        streamKey: stream?.cdn?.ingestionInfo?.streamName,
        rtmpUrl: stream?.cdn?.ingestionInfo?.ingestionAddress
      });

      return {
        id: result.data.id!,
        title: result.data.snippet!.title!,
        description: result.data.snippet!.description || '',
        scheduledStartTime: result.data.snippet!.scheduledStartTime!,
        status: result.data.status!.lifeCycleStatus!,
        streamId: streamId,
        streamKey: stream?.cdn?.ingestionInfo?.streamName,
        rtmpUrl: stream?.cdn?.ingestionInfo?.ingestionAddress,
      };
    } catch (error) {
      console.error('Error binding broadcast to stream:', error);
      throw new BadRequestException('Failed to bind broadcast to stream');
    }
  }

  async startBroadcast(userId: string, broadcastId: string): Promise<void> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      // First, check the current broadcast status
      const broadcastResponse = await youtube.liveBroadcasts.list({
        part: ['status'],
        id: [broadcastId],
      });

      const broadcast = broadcastResponse.data.items?.[0];
      if (!broadcast) {
        throw new BadRequestException('Broadcast not found');
      }

      const currentStatus = broadcast.status?.lifeCycleStatus;
      console.log('📺 Current broadcast status:', currentStatus);

      // Handle different states - transition directly to live (when OAuth app is published)
      if (currentStatus === 'live') {
        console.log('✅ Broadcast is already live');
        return;
      } else if (currentStatus === 'created' || currentStatus === 'ready') {
        // Transition: created/ready → live (requires published OAuth app)
        console.log(`📺 Transitioning broadcast from ${currentStatus} to live...`);
        await youtube.liveBroadcasts.transition({
          part: ['status'],
          id: broadcastId,
          broadcastStatus: 'live',
        });
        console.log('✅ Broadcast is now live!');
      } else if (currentStatus === 'testing') {
        // Transition: testing → live
        console.log('📺 Transitioning broadcast from testing to live...');
        await youtube.liveBroadcasts.transition({
          part: ['status'],
          id: broadcastId,
          broadcastStatus: 'live',
        });
        console.log('✅ Broadcast transitioned to live successfully');
      } else {
        console.log('⚠️ Broadcast is in unexpected state:', currentStatus);
      }

    } catch (error) {
      console.error('Error starting YouTube broadcast:', error);
      throw new BadRequestException('Failed to start YouTube broadcast');
    }
  }

  async stopBroadcast(userId: string, broadcastId: string): Promise<void> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      await youtube.liveBroadcasts.transition({
        part: ['status'],
        id: broadcastId,
        broadcastStatus: 'complete',
      });
    } catch (error) {
      console.error('Error stopping YouTube broadcast:', error);
      throw new BadRequestException('Failed to stop YouTube broadcast');
    }
  }

  async getBroadcast(userId: string, broadcastId: string): Promise<YouTubeBroadcast | null> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      const result = await youtube.liveBroadcasts.list({
        part: ['snippet', 'status'],
        id: [broadcastId],
      });

      const broadcast = result.data.items?.[0];
      if (!broadcast) {
        return null;
      }

      return {
        id: broadcast.id!,
        title: broadcast.snippet!.title!,
        description: broadcast.snippet!.description || '',
        scheduledStartTime: broadcast.snippet!.scheduledStartTime!,
        status: broadcast.status!.lifeCycleStatus!,
      };
    } catch (error) {
      console.error('Error getting YouTube broadcast:', error);
      return null;
    }
  }

  async getUserBroadcasts(userId: string): Promise<YouTubeBroadcast[]> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      const result = await youtube.liveBroadcasts.list({
        part: ['snippet', 'status'],
        mine: true,
        maxResults: 50,
      });

      return result.data.items?.map(broadcast => ({
        id: broadcast.id!,
        title: broadcast.snippet!.title!,
        description: broadcast.snippet!.description || '',
        scheduledStartTime: broadcast.snippet!.scheduledStartTime!,
        status: broadcast.status!.lifeCycleStatus!,
      })) || [];
    } catch (error) {
      console.error('Error getting user YouTube broadcasts:', error);
      return [];
    }
  }

  async getUserChannels(userId: string): Promise<YouTubeChannel[]> {
    try {
      const youtube = await this.getYouTubeClient(userId);

      const result = await youtube.channels.list({
        part: ['snippet', 'brandingSettings'],
        mine: true,
        maxResults: 50,
      });

      return result.data.items?.map(channel => ({
        id: channel.id!,
        title: channel.snippet!.title!,
        description: channel.snippet!.description || '',
        thumbnailUrl: channel.snippet!.thumbnails?.default?.url,
        customUrl: channel.snippet!.customUrl,
      })) || [];
    } catch (error) {
      console.error('Error getting user YouTube channels:', error);
      return [];
    }
  }
}
