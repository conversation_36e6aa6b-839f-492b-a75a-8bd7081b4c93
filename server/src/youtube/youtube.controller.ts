import { <PERSON>, Post, Get, Body, Param, UseGuards, Request } from '@nestjs/common';
import { YouTubeService } from './youtube.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateBroadcastDto } from './dto/create-broadcast.dto';
import { CreateStreamDto } from './dto/create-stream.dto';
import { BindBroadcastDto } from './dto/bind-broadcast.dto';

@Controller('youtube')
@UseGuards(JwtAuthGuard)
export class YouTubeController {
  constructor(private readonly youtubeService: YouTubeService) {}

  @Post('broadcasts')
  async createBroadcast(@Body() createBroadcastDto: CreateBroadcastDto, @Request() req) {
    return this.youtubeService.createBroadcast(
      req.user.userId,
      createBroadcastDto.title,
      createBroadcastDto.description,
      createBroadcastDto.scheduledStartTime ? new Date(createBroadcastDto.scheduledStartTime) : undefined,
      createBroadcastDto.privacy
    );
  }

  @Post('streams')
  async createStream(@Body() createStreamDto: CreateStreamDto, @Request() req) {
    return this.youtubeService.createStream(
      req.user.userId,
      createStreamDto.title,
      createStreamDto.description,
      createStreamDto.resolution
    );
  }

  @Post('broadcasts/:broadcastId/bind')
  async bindBroadcastToStream(
    @Param('broadcastId') broadcastId: string,
    @Body() bindBroadcastDto: BindBroadcastDto,
    @Request() req
  ) {
    return this.youtubeService.bindBroadcastToStream(
      req.user.userId,
      broadcastId,
      bindBroadcastDto.streamId
    );
  }

  @Post('broadcasts/:broadcastId/start')
  async startBroadcast(@Param('broadcastId') broadcastId: string, @Request() req) {
    await this.youtubeService.startBroadcast(req.user.userId, broadcastId);
    return { message: 'Broadcast started successfully' };
  }

  @Post('broadcasts/:broadcastId/stop')
  async stopBroadcast(@Param('broadcastId') broadcastId: string, @Request() req) {
    await this.youtubeService.stopBroadcast(req.user.userId, broadcastId);
    return { message: 'Broadcast stopped successfully' };
  }

  @Get('broadcasts')
  async getUserBroadcasts(@Request() req) {
    return this.youtubeService.getUserBroadcasts(req.user.userId);
  }

  @Get('broadcasts/:broadcastId')
  async getBroadcast(@Param('broadcastId') broadcastId: string, @Request() req) {
    return this.youtubeService.getBroadcast(req.user.userId, broadcastId);
  }

  @Get('channels')
  async getUserChannels(@Request() req) {
    return this.youtubeService.getUserChannels(req.user.userId);
  }

  @Get('debug/auth')
  async debugAuth(@Request() req) {
    try {
      const user = await this.youtubeService['usersService'].findById(req.user.userId);
      const googlePlatform = user.linkedPlatforms.find(p => p.provider === 'google');

      return {
        hasGooglePlatform: !!googlePlatform,
        hasAccessToken: !!googlePlatform?.accessToken,
        hasRefreshToken: !!googlePlatform?.refreshToken,
        tokenExpiresAt: googlePlatform?.tokenExpiresAt,
        isTokenExpired: googlePlatform?.tokenExpiresAt ? googlePlatform.tokenExpiresAt < new Date() : null,
        linkedAt: googlePlatform?.linkedAt,
      };
    } catch (error) {
      return { error: error.message };
    }
  }
}
