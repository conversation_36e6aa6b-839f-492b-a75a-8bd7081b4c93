import { IsEmail, IsString, Is<PERSON><PERSON>al, Is<PERSON>num, MinLength, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class LinkedPlatformDto {
  @IsEnum(['google', 'facebook', 'local'])
  provider: string;

  @IsString()
  providerId: string;

  @IsOptional()
  @IsString()
  accessToken?: string;

  @IsOptional()
  @IsString()
  refreshToken?: string;

  @IsOptional()
  tokenExpiresAt?: Date;
}

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  @IsOptional()
  password?: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsEnum(['local', 'google', 'facebook'])
  @IsOptional()
  primaryProvider?: string = 'local';

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LinkedPlatformDto)
  linkedPlatforms?: LinkedPlatformDto[];
}
