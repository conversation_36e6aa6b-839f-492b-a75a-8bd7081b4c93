# Deployment Guide

This guide covers deploying the WebRTC-to-RTMP streaming application with mediasoup service integration.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React App     │    │  Main Server     │    │ Mediasoup       │
│   (Frontend)    │◄──►│  (Signaling)     │◄──►│ Service         │
│                 │    │                  │    │ (Recording)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Static Files  │    │   Cloud Run      │    │   Cloud Run     │
│   (CDN/Storage) │    │   (Main App)     │    │   (Mediasoup)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Prerequisites

1. **Google Cloud Platform Account**
   - Project with billing enabled
   - Cloud Run API enabled
   - Cloud Build API enabled
   - Container Registry API enabled

2. **Local Development Tools**
   - Node.js 18+
   - Docker
   - Google Cloud SDK (`gcloud`)

3. **Domain & SSL (Optional)**
   - Custom domain for production
   - SSL certificate for HTTPS

## Deployment Steps

### 1. Prepare the Environment

```bash
# Clone and setup the project
git clone <your-repo>
cd poc-webrtc

# Install dependencies
npm install
cd server && npm install && cd ..
cd media-server && npm install && cd ..
```

### 2. Configure GCP

```bash
# Login to GCP
gcloud auth login

# Set your project ID
export PROJECT_ID="your-gcp-project-id"
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

### 3. Deploy Main Application

```bash
# Build the React frontend
npm run build

# Deploy the main server
cd server
gcloud run deploy webrtc-signaling \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 3001 \
  --memory 1Gi \
  --cpu 1 \
  --set-env-vars NODE_ENV=production
```

### 4. Setup Custom Domain (Optional)

```bash
# Map custom domain to Cloud Run service
gcloud run domain-mappings create \
  --service webrtc-signaling \
  --domain your-domain.com \
  --region us-central1
```

## Environment Configuration

### Main Application (.env)

```bash
NODE_ENV=production
PORT=3001
MEDIA_SERVER_URL=https://media-server-xxx-uc.a.run.app
```

### Mediasoup Service (.env)

```bash
NODE_ENV=production
PORT=8080
LISTEN_IP=0.0.0.0
ANNOUNCED_IP=  # Auto-detected
MEDIASOUP_LOG_LEVEL=warn
RTC_MIN_PORT=32256
RTC_MAX_PORT=65535
MAX_BITRATE=3000000
MAX_FRAMERATE=30.0
```

## Monitoring & Logging

### View Logs

```bash
# Main application logs
gcloud logs tail --service=webrtc-signaling

# Media server logs
gcloud logs tail --service=media-server
```

### Health Checks

```bash
# Check main application
curl https://your-app-url.com/

# Check media server
curl https://media-server-url.com/
```

### Metrics

Monitor in GCP Console:
- Cloud Run → Services → [Service Name] → Metrics
- Key metrics: Request count, latency, error rate, CPU/Memory usage

## Scaling Configuration

### Auto-scaling Settings

```bash
# Configure auto-scaling for main app
gcloud run services update webrtc-signaling \
  --region us-central1 \
  --min-instances 1 \
  --max-instances 10 \
  --concurrency 80

# Configure auto-scaling for mediasoup service
gcloud run services update mediasoup-recording \
  --region us-central1 \
  --min-instances 0 \
  --max-instances 5 \
  --concurrency 10  # Lower concurrency for media processing
```

### Resource Allocation

```bash
# Increase resources for mediasoup service
gcloud run services update mediasoup-recording \
  --region us-central1 \
  --memory 4Gi \
  --cpu 2
```

## Security Configuration

### Network Security

```bash
# Restrict access to mediasoup service (internal only)
gcloud run services update mediasoup-recording \
  --region us-central1 \
  --no-allow-unauthenticated

# Create service account for internal communication
gcloud iam service-accounts create mediasoup-client
gcloud run services add-iam-policy-binding mediasoup-recording \
  --region us-central1 \
  --member="serviceAccount:mediasoup-client@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/run.invoker"
```

### HTTPS Configuration

```bash
# Force HTTPS redirects
gcloud run services update webrtc-signaling \
  --region us-central1 \
  --set-env-vars FORCE_HTTPS=true
```

## Troubleshooting

### Common Issues

1. **WebRTC Connection Failures**
   ```bash
   # Check firewall rules for RTC ports
   gcloud compute firewall-rules list
   
   # Verify ANNOUNCED_IP setting
   gcloud run services describe mediasoup-recording \
     --region us-central1 --format="value(spec.template.spec.containers[0].env[?(@.name=='ANNOUNCED_IP')].value)"
   ```

2. **High Latency**
   ```bash
   # Check service regions and move closer to users
   gcloud run regions list
   
   # Increase CPU allocation
   gcloud run services update mediasoup-recording \
     --region us-central1 \
     --cpu 2
   ```

3. **Memory Issues**
   ```bash
   # Monitor memory usage
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=mediasoup-recording" \
     --format="value(jsonPayload.message)" \
     --limit 50
   
   # Increase memory allocation
   gcloud run services update mediasoup-recording \
     --region us-central1 \
     --memory 4Gi
   ```

### Debug Mode

```bash
# Enable debug logging
gcloud run services update mediasoup-recording \
  --region us-central1 \
  --set-env-vars MEDIASOUP_LOG_LEVEL=debug
```

## Cost Optimization

### Resource Management

1. **Set minimum instances to 0** for cost savings during low usage
2. **Use appropriate CPU/memory allocation** based on actual usage
3. **Monitor and adjust concurrency** settings
4. **Implement request timeout** to prevent hanging connections

### Monitoring Costs

```bash
# View Cloud Run costs
gcloud billing budgets list
gcloud logging read "protoPayload.serviceName=run.googleapis.com" \
  --format="table(timestamp,protoPayload.resourceName)"
```

## Backup & Recovery

### Configuration Backup

```bash
# Export service configurations
gcloud run services describe webrtc-signaling \
  --region us-central1 \
  --format export > webrtc-signaling-config.yaml

gcloud run services describe media-server \
  --region us-central1 \
  --format export > media-server-config.yaml
```

### Disaster Recovery

1. **Multi-region deployment** for high availability
2. **Database backups** if using persistent storage
3. **Container image versioning** for rollback capability

## Performance Tuning

### Mediasoup Optimization

```bash
# Optimize for high-quality streaming
gcloud run services update media-server \
  --region us-central1 \
  --set-env-vars MAX_BITRATE=8000000,MAX_FRAMERATE=60.0

# Optimize for low-latency
gcloud run services update media-server \
  --region us-central1 \
  --set-env-vars MAX_BITRATE=3000000,MAX_FRAMERATE=30.0
```

### Network Optimization

1. **Use Cloud CDN** for static assets
2. **Enable HTTP/2** for better performance
3. **Optimize WebSocket connections** for real-time communication

This deployment guide provides a comprehensive approach to deploying your WebRTC-to-RTMP streaming application on Google Cloud Platform with proper scaling, monitoring, and security configurations.
