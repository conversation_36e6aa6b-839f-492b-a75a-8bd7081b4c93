import React, { useEffect, useRef, useState, useCallback } from 'react';
import { LogOut, Users, Monitor, Mic, MicOff, Video, VideoOff, Share, Grid, Maximize2, Copy, Check, QrCode, X } from 'lucide-react';
import { useConnection } from '../contexts/ConnectionContext';
import { VideoCompositor } from './VideoCompositor';
import { RTMPStreaming } from './RTMPStreaming';
import { useTabVisibility } from '../hooks/useTabVisibility';
import QRCode from 'qrcode';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface ProductionCenterProps {
  productionId: string;
  eventName?: string;
  productionData?: any; // Full production data from creation
  onLeave: () => void;
}

type LayoutMode = 'grid' | 'focus' | 'pip';

export const ProductionCenter: React.FC<ProductionCenterProps> = ({ productionId, eventName, productionData, onLeave }) => {
  const { peersRef, peerIds, joinProduction, localStream, toggleAudio, toggleVideo, toggleScreenShare, isAudioEnabled, isVideoEnabled, isScreenSharing } = useConnection();
  const { showBackgroundWarning, dismissBackgroundWarning } = useTabVisibility();
  const [hasJoined, setHasJoined] = useState(false);
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('grid');
  const [canvasStream, setCanvasStream] = useState<MediaStream | null>(null);
  const [linkCopied, setLinkCopied] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [showVideoOverlay, setShowVideoOverlay] = useState(false);
  const [showIconOverlay, setShowIconOverlay] = useState(true);
  const [videoOverlayOpacity, setVideoOverlayOpacity] = useState(0);
  const [iconOverlayOpacity, setIconOverlayOpacity] = useState(1);
  const [overlayVideoReady, setOverlayVideoReady] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [actualProductionId, setActualProductionId] = useState(productionId);
  const [actualEventName, setActualEventName] = useState(eventName);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const canvasStreamRestartRef = useRef<(() => void) | null>(null);
  const overlayVideoRef = useRef<HTMLVideoElement>(null);

  // Initialize with props
  useEffect(() => {
    setActualProductionId(productionId);
    setActualEventName(eventName);
  }, [productionId, eventName]);

  useEffect(() => {
    if (!hasJoined && productionId) {
      joinProduction(productionId, true);
      setHasJoined(true);
    }
  }, [productionId, joinProduction, hasJoined]);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);



  // Load overlay video for thumbnail and preload for compositor
  useEffect(() => {
    if (overlayVideoRef.current) {
      const video = overlayVideoRef.current;

      // Try the primary video source first
      video.src = "https://www.switcherstudio.com/hubfs/Homepage%20Video%20Hero/homepage_hero_desktop_rev.webm";
      video.currentTime = 5; // Start 5 seconds in
      video.load();

      // Preload and play for compositor use
      video.addEventListener('loadeddata', () => {
        video.play().catch(console.error);
      });

      video.addEventListener('canplay', () => {
        setOverlayVideoReady(true); // Trigger re-render when video is ready
      });

      video.addEventListener('error', (e) => {
        console.error('Overlay video error:', e);
        // Fallback to a different source or handle error
      });
    }
  }, []);

  const handleCanvasStreamChange = useCallback((stream: MediaStream | null) => {
    setCanvasStream(stream);
  }, []);

  const handleCanvasStreamRestart = useCallback(() => {
    // Store the restart function to be called from RTMP component
    canvasStreamRestartRef.current = () => {
      // This will be set by the VideoCompositor component
      console.log('Canvas stream restart requested');
    };
  }, []);

  // Video overlay animation
  useEffect(() => {
    if (!showVideoOverlay) return;

    let animationId: number;
    const startTime = Date.now();
    const duration = 300; // 300ms

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Ease-in-out function
      const easeInOut = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      setVideoOverlayOpacity(easeInOut);

      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      }
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [showVideoOverlay]);

  // Icon overlay animation
  useEffect(() => {
    if (showIconOverlay) return;

    let animationId: number;
    const startTime = Date.now();
    const duration = 300; // 300ms

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Ease-in-out function for fade out
      const easeInOut = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      setIconOverlayOpacity(1 - easeInOut);

      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      }
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [showIconOverlay]);

  // Reverse animation for icon overlay when showing again
  useEffect(() => {
    if (!showIconOverlay) return;

    let animationId: number;
    const startTime = Date.now();
    const duration = 300; // 300ms

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Ease-in-out function
      const easeInOut = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      setIconOverlayOpacity(easeInOut);

      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      }
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [showIconOverlay]);

  const handleLeaveProduction = async () => {
    try {
      // Finish the production when host leaves
      console.log('Attempting to complete production:', actualProductionId);
      const response = await axios.post(`${API_BASE_URL}/productions/${actualProductionId}/complete`);
      console.log('Production completed successfully:', response.data);
    } catch (error: any) {
      console.error('Failed to finish production:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
    } finally {
      // Always call onLeave to return to the dashboard
      // Note: Media server disconnect is handled by RTMPStreaming component cleanup
      onLeave();
    }
  };

  const handleCopyLink = async () => {
    const joinUrl = `${window.location.origin}/join/${actualProductionId}`;
    try {
      await navigator.clipboard.writeText(joinUrl);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShowQRCode = async () => {
    const joinLink = `${window.location.origin}/join/${actualProductionId}`;
    try {
      const qrDataUrl = await QRCode.toDataURL(joinLink, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeDataUrl(qrDataUrl);
      setShowQRCode(true);
    } catch (err) {
      console.error('Failed to generate QR code:', err);
    }
  };

  return (
    <div className="min-h-screen p-2" style={{ backgroundColor: '#1a1f2e' }}>
      {/* Header */}
      <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3 mb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-orange-500/20 p-3 rounded-lg">
              <Monitor className="w-8 h-8 text-orange-500" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {actualEventName ? `Production Center: ${actualEventName}` : 'Production Center'}
              </h1>
              <div className="flex items-center space-x-2">
                <span className="text-gray-400">Share Link:</span>
                <button
                  onClick={handleCopyLink}
                  className="flex items-center space-x-2 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 hover:border-gray-500/50 text-gray-300 hover:text-white px-3 py-1 rounded-lg transition-all"
                  title={`Copy join link: ${window.location.origin}/join/${actualProductionId}`}
                >
                  <span className="font-mono text-sm">{actualProductionId}</span>
                  {linkCopied ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
                <button
                  onClick={handleShowQRCode}
                  className="flex items-center space-x-1 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 hover:border-gray-500/50 text-gray-300 hover:text-white px-2 py-1 rounded-lg transition-all"
                  title="Show QR Code"
                >
                  <QrCode className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleLeaveProduction}
              className="bg-gray-700/50 border border-red-500/30 hover:bg-gray-600/50 hover:border-red-500/50 text-red-400 hover:text-red-300 px-4 py-3 rounded-lg transition-all flex items-center space-x-2"
            >
              <LogOut className="w-5 h-5" />
              <span>Finish Production</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Background Warning */}
      {showBackgroundWarning && isStreaming && (
        <div className="bg-yellow-600/20 border border-yellow-500/50 rounded-xl p-4 mb-2 backdrop-blur-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-yellow-500/20 p-2 rounded-lg">
                <Monitor className="w-5 h-5 text-yellow-500" />
              </div>
              <div>
                <h3 className="text-yellow-200 font-semibold">Keep This Tab Active</h3>
                <p className="text-yellow-300/80 text-sm">
                  This tab was backgrounded during your production. For optimal streaming performance, keep this browser tab active and visible.
                </p>
              </div>
            </div>
            <button
              onClick={dismissBackgroundWarning}
              className="text-yellow-400 hover:text-yellow-200 transition-colors"
              title="Dismiss warning"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-2">
        {/* Main Composite View */}
        <div className="xl:col-span-3">
          <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-2">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-xl font-bold text-white">Live Composite</h2>
              <div className="flex items-center space-x-4">
                {/* Layout Controls */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => setLayoutMode('grid')}
                    className={`p-2 rounded-lg border transition-all ${
                      layoutMode === 'grid'
                        ? 'bg-gray-600/50 border-gray-500/50 text-white'
                        : 'bg-gray-700/30 border-gray-600/30 text-gray-400 hover:bg-gray-600/40 hover:border-gray-500/40 hover:text-white'
                    }`}
                    title="Grid Layout"
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('focus')}
                    className={`p-2 rounded-lg border transition-all ${
                      layoutMode === 'focus'
                        ? 'bg-gray-600/50 border-gray-500/50 text-white'
                        : 'bg-gray-700/30 border-gray-600/30 text-gray-400 hover:bg-gray-600/40 hover:border-gray-500/40 hover:text-white'
                    }`}
                    title="Focus Layout"
                  >
                    <Maximize2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('pip')}
                    className={`p-2 rounded-lg border transition-all ${
                      layoutMode === 'pip'
                        ? 'bg-gray-600/50 border-gray-500/50 text-white'
                        : 'bg-gray-700/30 border-gray-600/30 text-gray-400 hover:bg-gray-600/40 hover:border-gray-500/40 hover:text-white'
                    }`}
                    title="Picture-in-Picture"
                  >
                    <Monitor className="w-4 h-4" />
                  </button>

                </div>

                {/* Live Composite Status Indicator */}
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    isStreaming ? 'bg-red-500 animate-pulse' :
                    canvasStream ? 'bg-green-500' : 'bg-gray-500'
                  }`}></div>
                  <span className="text-gray-300">
                    {isStreaming ? 'Live Streaming' :
                     canvasStream ? 'Live Composite' : 'Offline'}
                  </span>
                </div>

                {/* Composite Status */}
                <div className="bg-gray-700/50 rounded-lg px-3 py-1">
                  <span className="text-white text-sm">
                    Layout: {layoutMode.toUpperCase()} • {peerIds.length} participants • {localStream ? 'Host' : 'No host'}
                  </span>
                </div>
              </div>
            </div>

            <VideoCompositor
              peers={peersRef.current || {}}
              peerIds={peerIds}
              localStream={localStream}
              overlayVideoUrl="https://www.switcherstudio.com/hubfs/Homepage%20Video%20Hero/homepage_hero_desktop_rev.webm"
              overlayVideoElement={overlayVideoRef.current}
              showOverlays={false}
              layoutMode={layoutMode}
              onLayoutModeChange={setLayoutMode}
              onCanvasStreamChange={handleCanvasStreamChange}
              onCanvasStreamRestart={handleCanvasStreamRestart}
              showVideoOverlay={showVideoOverlay}
              showIconOverlay={showIconOverlay}
              videoOverlayOpacity={videoOverlayOpacity}
              iconOverlayOpacity={iconOverlayOpacity}
            />
          </div>
        </div>

        {/* Control Panel */}
        <div className="space-y-2">
          {/* Local Camera & Controls */}
          <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3">
            <h3 className="text-lg font-bold text-white mb-3">Local Camera</h3>

            <div className="flex items-start space-x-3">
              {/* Camera Controls */}
              <div className="flex flex-col space-y-2">
                <button
                  onClick={toggleAudio}
                  className={`p-2 rounded-lg border transition-all ${
                    isAudioEnabled
                      ? 'bg-gray-700/50 border-green-500/30 text-green-400 hover:bg-gray-600/50'
                      : 'bg-gray-700/50 border-red-500/30 text-red-400 hover:bg-gray-600/50'
                  }`}
                  title={isAudioEnabled ? 'Mute Audio' : 'Unmute Audio'}
                >
                  {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={toggleVideo}
                  className={`p-2 rounded-lg border transition-all ${
                    isVideoEnabled
                      ? 'bg-gray-700/50 border-green-500/30 text-green-400 hover:bg-gray-600/50'
                      : 'bg-gray-700/50 border-red-500/30 text-red-400 hover:bg-gray-600/50'
                  }`}
                  title={isVideoEnabled ? 'Turn Off Camera' : 'Turn On Camera'}
                >
                  {isVideoEnabled ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={toggleScreenShare}
                  className={`p-2 rounded-lg border transition-all ${
                    isScreenSharing
                      ? 'bg-gray-700/50 border-blue-500/30 text-blue-400 hover:bg-gray-600/50'
                      : 'bg-gray-700/50 border-gray-600/30 text-gray-400 hover:bg-gray-600/50 hover:text-white'
                  }`}
                  title={isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
                >
                  <Share className="w-4 h-4" />
                </button>
              </div>

              {/* Camera Thumbnail */}
              <div className="relative bg-gray-900 rounded-lg overflow-hidden w-24 h-16">
                <video
                  ref={localVideoRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Overlay Controls */}
            <div className="mt-4">
              <h4 className="text-sm font-semibold text-gray-300 mb-2">Overlay Controls</h4>
              <div className="flex space-x-3">
                {/* Video Overlay Toggle */}
                <button
                  onClick={() => setShowVideoOverlay(!showVideoOverlay)}
                  className={`relative group transition-all duration-200 border rounded-lg ${
                    showVideoOverlay
                      ? 'border-gray-500/50 bg-gray-600/30'
                      : 'border-gray-600/30 bg-gray-700/30 hover:border-gray-500/40 hover:bg-gray-600/30'
                  }`}
                  title="Toggle Video Overlay"
                >
                  <div className="w-16 h-12 bg-gray-900 rounded-lg overflow-hidden">
                    <video
                      ref={overlayVideoRef}
                      autoPlay
                      muted
                      playsInline
                      loop
                      crossOrigin="anonymous"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className={`absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center transition-opacity ${
                    showVideoOverlay ? 'opacity-0' : 'opacity-100'
                  }`}>
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </button>

                {/* Icon Overlay Toggle */}
                <button
                  onClick={() => setShowIconOverlay(!showIconOverlay)}
                  className={`relative group transition-all duration-200 border rounded-lg ${
                    showIconOverlay
                      ? 'border-gray-500/50 bg-gray-600/30'
                      : 'border-gray-600/30 bg-gray-700/30 hover:border-gray-500/40 hover:bg-gray-600/30'
                  }`}
                  title="Toggle Icon Overlay"
                >
                  <div className="w-16 h-12 bg-gray-900 rounded-lg overflow-hidden flex items-center justify-center">
                    <img
                      src="/switcher-logo.svg"
                      alt="Logo"
                      className="w-8 h-8 object-contain"
                    />
                  </div>
                  <div className={`absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center transition-opacity ${
                    showIconOverlay ? 'opacity-0' : 'opacity-100'
                  }`}>
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Additional Cameras - Only show if there are participants */}
          {peerIds.length > 0 && (
            <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3">
              <h3 className="text-lg font-bold text-white mb-2">Additional Cameras</h3>
              <div className="space-y-1">
                {peerIds.map((peerId) => (
                  <div key={peerId} className="flex items-center justify-between bg-gray-700/50 rounded-lg p-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-white">{peerId.substring(0, 8)}...</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-green-400 text-sm">Connected</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* RTMP Streaming */}
          <RTMPStreaming
            canvasStream={canvasStream}
            isHost={true}
            productionId={actualProductionId}
            productionData={productionData}
            onRestartCanvasStream={() => canvasStreamRestartRef.current?.()}
            onStreamingStatusChange={setIsStreaming}
          />
        </div>
      </div>

      {/* QR Code Popover */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setShowQRCode(false)}>
          <div className="bg-gray-800 border border-gray-600 rounded-2xl p-6 max-w-sm mx-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">Join Production</h3>
              <button
                onClick={() => setShowQRCode(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="text-center">
              <div className="bg-white p-4 rounded-lg mb-4 inline-block">
                {qrCodeDataUrl && (
                  <img src={qrCodeDataUrl} alt="QR Code" className="w-48 h-48" />
                )}
              </div>

              <p className="text-gray-300 text-sm mb-2">
                Scan with your phone to join this production
              </p>

              <div className="bg-gray-700/50 rounded-lg p-3">
                <p className="text-white font-mono text-lg text-center">{actualProductionId}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
