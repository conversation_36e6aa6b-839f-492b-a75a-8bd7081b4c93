import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  LayoutMode,
  calculateVideoViewport,
  calculateOverlayViewport,
  calculateLogoViewport
} from './webgl/layoutUtils';
import { WebGLEngine } from './webgl/WebGLEngine';
import { VideoElementManager } from './video/VideoElementManager';
import { OverlayManager } from './video/OverlayManager';
import { LayoutControls } from './ui/LayoutControls';
import { StatusOverlay } from './ui/StatusOverlay';

interface VideoCompositorProps {
  peers: Record<string, any>;
  peerIds: string[];
  localStream: MediaStream | null;
  overlayVideoUrl?: string;
  overlayVideoElement?: HTMLVideoElement | null;
  showOverlays?: boolean;
  layoutMode: LayoutMode;
  onLayoutModeChange: (mode: LayoutMode) => void;
  onCanvasStreamChange?: (stream: MediaStream | null) => void;
  onCanvasStreamRestart?: (restartFn: () => void) => void;
  showVideoOverlay?: boolean;
  showIconOverlay?: boolean;
  videoOverlayOpacity?: number;
  iconOverlayOpacity?: number;
}



export const VideoCompositor: React.FC<VideoCompositorProps> = ({
  peers,
  peerIds,
  localStream,
  overlayVideoUrl,
  overlayVideoElement,
  showOverlays = false,
  layoutMode: externalLayoutMode,
  onLayoutModeChange,
  onCanvasStreamChange,
  onCanvasStreamRestart,
  showVideoOverlay = false,
  showIconOverlay = true,
  videoOverlayOpacity = 1,
  iconOverlayOpacity = 1
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [internalLayoutMode, setInternalLayoutMode] = useState<LayoutMode>('grid');
  const canvasStreamRef = useRef<MediaStream | null>(null);

  // Use external layout mode if provided, otherwise use internal state
  const layoutMode = showOverlays ? internalLayoutMode : externalLayoutMode;

  const webglEngineRef = useRef<WebGLEngine | null>(null);
  const videoManagerRef = useRef<VideoElementManager | null>(null);
  const overlayManagerRef = useRef<OverlayManager | null>(null);

  // Refs to access current overlay values in the render loop
  const overlayVideoElementRef = useRef(overlayVideoElement);
  const showVideoOverlayRef = useRef(showVideoOverlay);
  const showIconOverlayRef = useRef(showIconOverlay);
  const videoOverlayOpacityRef = useRef(videoOverlayOpacity);
  const iconOverlayOpacityRef = useRef(iconOverlayOpacity);

  // Update refs when props change
  useEffect(() => {
    overlayVideoElementRef.current = overlayVideoElement;
    showVideoOverlayRef.current = showVideoOverlay;
    showIconOverlayRef.current = showIconOverlay;
    videoOverlayOpacityRef.current = videoOverlayOpacity;
    iconOverlayOpacityRef.current = iconOverlayOpacity;
  }, [overlayVideoElement, showVideoOverlay, showIconOverlay, videoOverlayOpacity, iconOverlayOpacity]);

  // Canvas stream capture functionality
  const startCanvasStream = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return null;

    try {
      // Stop existing stream if any
      if (canvasStreamRef.current) {
        canvasStreamRef.current.getTracks().forEach(track => track.stop());
      }

      // Capture the canvas as a MediaStream
      const stream = canvas.captureStream(30); // 30 FPS
      canvasStreamRef.current = stream;

      // Monitor track events and auto-restart if needed
      stream.getTracks().forEach((track) => {
        track.addEventListener('ended', () => {
          console.log('⚠️ Canvas track ended, restarting stream...', track.kind);
          // Restart the stream after a short delay
          setTimeout(() => {
            if (canvasRef.current) {
              startCanvasStream();
            }
          }, 100);
        });
      });

      if (onCanvasStreamChange) {
        onCanvasStreamChange(stream);
      }

      console.log('✅ Canvas stream started');
      return stream;
    } catch (error) {
      console.error('❌ Failed to capture canvas stream:', error);
      return null;
    }
  }, [onCanvasStreamChange]);

  const stopCanvasStream = useCallback(() => {
    if (canvasStreamRef.current) {
      // Stop all tracks in the stream
      canvasStreamRef.current.getTracks().forEach(track => track.stop());
      canvasStreamRef.current = null;

      if (onCanvasStreamChange) {
        onCanvasStreamChange(null);
      }

      console.log('⏹️ Canvas stream stopped');
    }
  }, [onCanvasStreamChange]);

  const restartCanvasStream = useCallback(() => {
    console.log('🔄 Restarting canvas stream...');

    // Don't notify about null state during restart to prevent UI flashing
    if (canvasStreamRef.current) {
      canvasStreamRef.current.getTracks().forEach(track => track.stop());
      canvasStreamRef.current = null;
    }

    // Start new stream immediately without notifying about null state
    setTimeout(() => {
      startCanvasStream();
    }, 100);
  }, [startCanvasStream]);

  // Initialize managers
  useEffect(() => {
    if (!videoManagerRef.current) {
      videoManagerRef.current = new VideoElementManager();
    }

    if (!overlayManagerRef.current) {
      overlayManagerRef.current = new OverlayManager();
      // Set up logo image immediately
      overlayManagerRef.current.setupLogoImage();
    }
  }, []);

  // Expose restart function to parent
  useEffect(() => {
    if (onCanvasStreamRestart) {
      onCanvasStreamRestart(restartCanvasStream);
    }
  }, [onCanvasStreamRestart, restartCanvasStream]);







  const initWebGL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return false;
    }

    if (!webglEngineRef.current) {
      webglEngineRef.current = new WebGLEngine();
    }

    const result = webglEngineRef.current.initialize(canvas);
    return result;
  }, []);









  // Clean up video overlay texture when disabled
  useEffect(() => {
    const engine = webglEngineRef.current;
    if (!showVideoOverlay && engine) {
      engine.removeTexture('overlay');
    }
  }, [showVideoOverlay]);

  // Handle logo overlay - only setup when needed
  useEffect(() => {
    const overlayManager = overlayManagerRef.current;
    const engine = webglEngineRef.current;
    if (!overlayManager) return;

    if (showIconOverlay) {
      overlayManager.setupLogoImage();
    } else {
      overlayManager.removeLogoImage();
      // Clean up logo texture when disabled
      if (engine) {
        engine.removeTexture('logo');
      }
    }
  }, [showIconOverlay]);



  // Handle local stream
  useEffect(() => {
    const videoManager = videoManagerRef.current;
    if (!videoManager) return;

    if (localStream) {
      videoManager.getOrCreateLocalVideo(localStream);
    } else {
      videoManager.removeLocalVideo();
    }
  }, [localStream]);

  // Handle peer streams
  useEffect(() => {
    const videoManager = videoManagerRef.current;
    if (!videoManager) return;

    // Create or update video elements for each peer
    Object.entries(peers).forEach(([peerId, peer]) => {
      videoManager.getOrCreatePeerVideo(peerId, peer);
    });

    // Clean up removed peers
    const { peers: currentPeerVideos } = videoManager.getAllVideos();
    Object.keys(currentPeerVideos).forEach(peerId => {
      if (!peers[peerId]) {
        videoManager.removePeerVideo(peerId);
      }
    });
  }, [peers, peerIds]);



  // Initialize WebGL on mount
  useEffect(() => {
    if (!initWebGL()) {
      console.error('❌ WebGL initialization failed, using 2D canvas fallback');
      // Test 2D canvas as fallback
      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#ff00ff'; // Magenta
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        } else {
          console.error('❌ 2D canvas context also failed');
        }
      }
      return;
    }
    console.log('✅ WebGL initialized successfully');

    // Start canvas stream capture after WebGL initialization
    setTimeout(() => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      // Log detailed canvas info
      console.log('📊 Canvas dimensions:', canvas.width, 'x', canvas.height);

      // Start the canvas stream
      startCanvasStream();
    }, 100); // Small delay to ensure canvas is ready
  }, [initWebGL, startCanvasStream]);

  // Main rendering loop
  useEffect(() => {
    const canvas = canvasRef.current;
    const engine = webglEngineRef.current;

    if (!canvas) {
      return;
    }

    if (!engine) {
      return;
    }

    const draw = () => {
      const videoManager = videoManagerRef.current;
      const engine = webglEngineRef.current;

      if (!videoManager) {
        return;
      }

      if (!engine) {
        return;
      }

      // Collect all available videos (local + peers), excluding overlay video
      const allVideos = videoManager.getReadyVideos().filter(video => {
        // Exclude the overlay video from regular rendering
        return video !== overlayVideoElementRef.current;
      });

      if (allVideos.length === 0) {
        // Clear the canvas with gray background
        engine.clear(0.22, 0.25, 0.31, 1.0);
        // Occasionally warn about no videos
        if (Math.random() < 0.001) {
          console.warn('⚠️ No videos ready for rendering');
        }
      } else {
        // Clear the canvas for video rendering
        engine.clear(0.0, 0.0, 0.0, 1.0);
      }

      // Update textures and render videos
      allVideos.forEach((video, index) => {
        // Determine video ID using the video manager
        const videoId = videoManager.getVideoId(video);

        // Get or create texture for this video
        let texture = engine.getTexture(videoId);
        if (!texture) {
          const newTexture = engine.createVideoTexture(videoId, video);
          if (newTexture) {
            texture = newTexture;
          } else {
            return;
          }
        }

        if (texture && video.videoWidth > 0 && video.videoHeight > 0) {
          try {
            // Update texture with current video frame
            engine.updateVideoTexture(texture, video);

            // Calculate viewport based on layout
            const viewport = calculateVideoViewport(index, allVideos.length, canvas.width, canvas.height, layoutMode);

            // Render the video with aspect ratio preservation
            const borderColor: [number, number, number] = videoId === 'local' ? [0, 0.83, 1] : [0.54, 0.31, 0.96]; // Blue for local, purple for peers
            engine.renderVideo(texture, viewport, borderColor, 1.0, 0.02, video.videoWidth, video.videoHeight);
          } catch (error) {
            console.error(`❌ Failed to render video ${videoId}:`, error);
          }
        }
      });

      // Render overlay video only if it's enabled and visible
      const overlayManager = overlayManagerRef.current;
      const overlayVideo = overlayVideoElementRef.current || (overlayManager ? overlayManager.getOverlayVideo() : null);
      const isOverlayVideoReady = overlayVideo &&
        overlayVideo.readyState >= overlayVideo.HAVE_CURRENT_DATA &&
        overlayVideo.videoWidth > 0 &&
        overlayVideo.videoHeight > 0;
      const shouldRenderVideoOverlay = isOverlayVideoReady && showVideoOverlayRef.current && videoOverlayOpacityRef.current > 0;



      if (shouldRenderVideoOverlay) {
        const overlayVideoId = 'overlay';

        // Get or create texture for overlay video
        let overlayTexture = engine.getTexture(overlayVideoId);
        if (!overlayTexture) {
          const newTexture = engine.createVideoTexture(overlayVideoId, overlayVideo);
          if (newTexture) {
            overlayTexture = newTexture;
          }
        }

        if (overlayTexture) {
          try {
            // Update texture with current overlay video frame
            engine.updateVideoTexture(overlayTexture, overlayVideo);

            // Calculate overlay viewport (bottom-right, 50% width and height)
            const overlayViewport = calculateOverlayViewport(canvas.width, canvas.height);

            // Render the overlay video with a distinct border color (green) and aspect ratio preservation
            engine.renderVideo(overlayTexture, overlayViewport, [0, 1, 0.5], videoOverlayOpacityRef.current, 0.02, overlayVideo.videoWidth, overlayVideo.videoHeight); // Green border, animated opacity
          } catch (error) {
            console.error('❌ Failed to render overlay video:', error);
          }
        }
      }

      // Render logo image only if it's enabled and visible (even with no videos)
      const shouldRenderIconOverlay = overlayManager && overlayManager.isLogoImageReady() && showIconOverlayRef.current && iconOverlayOpacityRef.current > 0;

      if (shouldRenderIconOverlay) {
        const logoImage = overlayManager.getLogoImage()!;
        const logoId = 'logo';

        // Get or create texture for logo image
        let logoTexture = engine.getTexture(logoId);
        if (!logoTexture) {
          const newTexture = engine.createImageTexture(logoId, logoImage);
          if (newTexture) {
            logoTexture = newTexture;
          }
        }

        if (logoTexture) {
          try {
            // Calculate logo viewport (top-right, 10% size)
            const logoViewport = calculateLogoViewport(canvas.width, canvas.height);

            // Render the logo with no border and aspect ratio preservation
            engine.renderVideo(logoTexture, logoViewport, [0, 0, 0], iconOverlayOpacityRef.current, 0, logoImage.width, logoImage.height); // No border, animated opacity
          } catch (error) {
            console.error('❌ Failed to render logo:', error);
          }
        }
      }
    };

    const animate = () => {
      draw();
      requestAnimationFrame(animate);
    };

    animate();

    // Cleanup function
    return () => {
      // Clean up WebGL textures
      if (engine) {
        engine.cleanup();
      }

      // Clean up video manager
      if (videoManagerRef.current) {
        videoManagerRef.current.cleanup();
      }

      // Clean up overlay manager
      if (overlayManagerRef.current) {
        overlayManagerRef.current.cleanup();
      }

      // Clean up canvas stream
      if (canvasStreamRef.current) {
        canvasStreamRef.current.getTracks().forEach(track => track.stop());
        canvasStreamRef.current = null;
        if (onCanvasStreamChange) {
          onCanvasStreamChange(null);
        }
      }
    };
  }, []); // Don't recreate on layout changes - layout is handled in render logic

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden">
      {/* Layout Controls */}
      <LayoutControls
        layoutMode={layoutMode}
        onLayoutModeChange={setInternalLayoutMode}
        showOverlays={showOverlays}
      />

      {/* Composite Canvas */}
      <canvas
        ref={canvasRef}
        width={1920}
        height={1080}
        className="w-full max-w-2xl aspect-video"
      />

      {/* Status Overlay */}
      <StatusOverlay
        layoutMode={layoutMode}
        peerCount={Object.keys(peers).length}
        hasLocalStream={!!localStream}
        showOverlays={showOverlays}
      />
    </div>
  );
};
