import React, { useState, useEffect } from 'react';
import { Plus, X, Youtube, Facebook } from 'lucide-react';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface YouTubeChannel {
  id: string;
  title: string;
  description: string;
  thumbnailUrl?: string;
  customUrl?: string;
}

interface StreamingDestination {
  platform: 'youtube' | 'facebook';
  channelId: string;
  channelName?: string;
  privacy?: 'public' | 'unlisted' | 'private';
}

interface StreamingDestinationsProps {
  destinations: StreamingDestination[];
  onChange: (destinations: StreamingDestination[]) => void;
  privacy: 'public' | 'unlisted' | 'private';
  onPrivacyChange: (privacy: 'public' | 'unlisted' | 'private') => void;
}

export const StreamingDestinations: React.FC<StreamingDestinationsProps> = ({
  destinations,
  onChange,
  privacy,
  onPrivacyChange,
}) => {
  const [youtubeChannels, setYoutubeChannels] = useState<YouTubeChannel[]>([]);
  const [loadingChannels, setLoadingChannels] = useState(false);
  const [showAddDestination, setShowAddDestination] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<'youtube' | 'facebook' | null>(null);
  const [selectedChannelId, setSelectedChannelId] = useState('');
  const [error, setError] = useState('');

  // Load YouTube channels when component mounts
  useEffect(() => {
    loadYouTubeChannels();
  }, []);

  const loadYouTubeChannels = async () => {
    setLoadingChannels(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/youtube/channels`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      setYoutubeChannels(response.data);
    } catch (err: any) {
      console.error('Failed to load YouTube channels:', err);
      // Don't show error for missing YouTube auth - user might not have linked account
      if (err.response?.status !== 401) {
        setError('Failed to load YouTube channels');
      }
    } finally {
      setLoadingChannels(false);
    }
  };

  const handleAddDestination = () => {
    if (!selectedPlatform || !selectedChannelId) return;

    let channelName = '';
    if (selectedPlatform === 'youtube') {
      const channel = youtubeChannels.find(c => c.id === selectedChannelId);
      channelName = channel?.title || '';
    }

    const newDestination: StreamingDestination = {
      platform: selectedPlatform,
      channelId: selectedChannelId,
      channelName,
      privacy,
    };

    // Check if destination already exists
    const exists = destinations.some(
      d => d.platform === selectedPlatform && d.channelId === selectedChannelId
    );

    if (exists) {
      setError('This destination is already added');
      return;
    }

    onChange([...destinations, newDestination]);
    setShowAddDestination(false);
    setSelectedPlatform(null);
    setSelectedChannelId('');
    setError('');
  };

  const handleRemoveDestination = (index: number) => {
    const newDestinations = destinations.filter((_, i) => i !== index);
    onChange(newDestinations);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'youtube':
        return <Youtube className="w-5 h-5 text-red-500" />;
      case 'facebook':
        return <Facebook className="w-5 h-5 text-blue-500" />;
      default:
        return <Globe className="w-5 h-5" />;
    }
  };



  return (
    <div className="space-y-4">
      {/* Privacy Setting - Only show if there are YouTube destinations */}
      {destinations.some(dest => dest.platform === 'youtube') && (
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Privacy Setting
          </label>
          <select
            value={privacy}
            onChange={(e) => onPrivacyChange(e.target.value as 'public' | 'unlisted' | 'private')}
            className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
          >
            <option value="unlisted">Unlisted</option>
            <option value="public">Public</option>
            <option value="private">Private</option>
          </select>
        </div>
      )}

      {/* Destinations List */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="block text-sm font-medium text-gray-300">
            Streaming Destinations
          </label>
          <button
            onClick={() => setShowAddDestination(true)}
            className="bg-gray-700/50 border border-gray-600/30 hover:bg-gray-600/50 hover:border-gray-500/50 text-white px-3 py-1.5 rounded-lg text-sm font-medium transition-all flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>Add Destination</span>
          </button>
        </div>

        {/* Current Destinations */}
        {destinations.length > 0 ? (
          <div className="space-y-2">
            {destinations.map((destination, index) => (
              <div
                key={`${destination.platform}-${destination.channelId}`}
                className="bg-gray-700/50 border border-gray-600/30 rounded-lg p-3 flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  {getPlatformIcon(destination.platform)}
                  <div>
                    <div className="text-white font-medium">
                      {destination.channelName || destination.channelId}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {destination.platform === 'youtube' ? 'YouTube Channel' : 'Facebook Page'}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => handleRemoveDestination(index)}
                  className="text-gray-400 hover:text-red-400 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-700/30 border border-gray-600/20 rounded-lg p-4 text-center text-gray-400">
            No streaming destinations added yet
          </div>
        )}
      </div>

      {/* Add Destination Modal */}
      {showAddDestination && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 border border-gray-700 rounded-2xl p-6 max-w-md w-full">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-bold text-white">Add Streaming Destination</h4>
              <button
                onClick={() => {
                  setShowAddDestination(false);
                  setSelectedPlatform(null);
                  setSelectedChannelId('');
                  setError('');
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            <div className="space-y-4">
              {/* Platform Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Platform
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => {
                      setSelectedPlatform('youtube');
                      // If there's only one YouTube channel, auto-select it and add the destination
                      if (youtubeChannels.length === 1) {
                        const channel = youtubeChannels[0];
                        const newDestination: StreamingDestination = {
                          platform: 'youtube',
                          channelId: channel.id,
                          channelName: channel.title,
                          privacy,
                        };

                        // Check if destination already exists
                        const exists = destinations.some(
                          d => d.platform === 'youtube' && d.channelId === channel.id
                        );

                        if (!exists) {
                          onChange([...destinations, newDestination]);
                          setShowAddDestination(false);
                          setSelectedPlatform(null);
                          setSelectedChannelId('');
                          setError('');
                        } else {
                          setError('This destination is already added');
                        }
                      }
                    }}
                    className={`p-3 rounded-lg border transition-all flex items-center justify-center space-x-2 ${
                      selectedPlatform === 'youtube'
                        ? 'bg-red-500/20 border-red-500/50 text-red-400'
                        : 'bg-gray-700/50 border-gray-600/30 text-gray-300 hover:bg-gray-600/50'
                    }`}
                  >
                    <Youtube className="w-5 h-5" />
                    <span>YouTube</span>
                  </button>
                  <button
                    onClick={() => setSelectedPlatform('facebook')}
                    disabled
                    className="p-3 rounded-lg border bg-gray-700/30 border-gray-600/20 text-gray-500 cursor-not-allowed flex items-center justify-center space-x-2"
                  >
                    <Facebook className="w-5 h-5" />
                    <span>Facebook</span>
                  </button>
                </div>
                <p className="text-gray-500 text-xs mt-1">Facebook integration coming soon</p>
              </div>

              {/* Channel Selection */}
              {selectedPlatform === 'youtube' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    YouTube Channel
                  </label>
                  {loadingChannels ? (
                    <div className="bg-gray-700/50 border border-gray-600/30 rounded-lg p-3 text-center text-gray-400">
                      Loading channels...
                    </div>
                  ) : youtubeChannels.length > 0 ? (
                    <select
                      value={selectedChannelId}
                      onChange={(e) => setSelectedChannelId(e.target.value)}
                      className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                    >
                      <option value="">Select a channel</option>
                      {youtubeChannels.map((channel) => (
                        <option key={channel.id} value={channel.id}>
                          {channel.title}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                      <p className="text-yellow-400 text-sm">
                        No YouTube channels found. Please link your Google account first.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddDestination(false);
                  setSelectedPlatform(null);
                  setSelectedChannelId('');
                  setError('');
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddDestination}
                disabled={!selectedPlatform || !selectedChannelId}
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Destination
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
