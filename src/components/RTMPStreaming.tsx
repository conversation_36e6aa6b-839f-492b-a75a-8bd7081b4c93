import React, { useCallback, useEffect, useState, useRef } from 'react';
import { useMediaServer } from '../hooks/useMediaServer';
import { useConnection } from '../contexts/ConnectionContext';
import { useTabVisibility } from '../hooks/useTabVisibility';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface RTMPStreamingProps {
  canvasStream: MediaStream | null;
  isHost: boolean;
  productionId?: string;
  productionData?: any; // Production data passed from parent
  onRestartCanvasStream?: () => void;
  onStreamingStatusChange?: (isStreaming: boolean) => void;
}

export const RTMPStreaming: React.FC<RTMPStreamingProps> = ({
  canvasStream,
  isHost,
  productionId,
  productionData: initialProductionData,
  onRestartCanvasStream,
  onStreamingStatusChange,
}) => {
  const { connectionStatus } = useConnection();
  const { isVisible: isTabVisible } = useTabVisibility();

  const {
    state,
    connect,
    startProducing,
    setRTMPUrl,
    startRTMPStream,
    stopRTMPStream,
    disconnect,
  } = useMediaServer();

  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isRestartingCanvas, setIsRestartingCanvas] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);
  const [productionData, setProductionData] = useState<any>(initialProductionData || null);
  const [rtmpUrl, setRtmpUrl] = useState<string>('');
  const hasAttemptedConnect = useRef(false);
  const [serverRequestedProducers, setServerRequestedProducers] = useState(false);
  const [wasTabBackgrounded, setWasTabBackgrounded] = useState(false);
  const tabVisibilityTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Track tab visibility changes for smoother error message transitions
  useEffect(() => {
    if (!isTabVisible) {
      // Tab became hidden - immediately mark as backgrounded
      setWasTabBackgrounded(true);
      // Clear any pending timeout
      if (tabVisibilityTimeoutRef.current) {
        clearTimeout(tabVisibilityTimeoutRef.current);
        tabVisibilityTimeoutRef.current = null;
      }
    } else if (wasTabBackgrounded) {
      // Tab became visible again after being backgrounded
      // Wait a few seconds before clearing the backgrounded state
      // This prevents jarring message switches
      tabVisibilityTimeoutRef.current = setTimeout(() => {
        setWasTabBackgrounded(false);
        tabVisibilityTimeoutRef.current = null;
      }, 3000); // 3 second delay
    }

    // Cleanup timeout on unmount
    return () => {
      if (tabVisibilityTimeoutRef.current) {
        clearTimeout(tabVisibilityTimeoutRef.current);
        tabVisibilityTimeoutRef.current = null;
      }
    };
  }, [isTabVisible, wasTabBackgrounded]);

  // Extract RTMP URL from production data (either passed as prop or fetched)
  useEffect(() => {
    if (!isHost) return;

    const extractRtmpUrl = (production: any) => {
      if (!production) return null;

      console.log('🎯 Production destinations:', production.destinations);
      console.log('🎯 Production legacy fields:', { rtmpUrl: production.rtmpUrl, rtmpStreamKey: production.rtmpStreamKey });

      // Get RTMP URL from destinations (find one with RTMP details) or fallback to legacy fields
      let rtmpEndpoint = '';
      if (production.destinations && production.destinations.length > 0) {
        console.log('🔍 All destinations:', production.destinations);

        // Find destination with RTMP details
        const rtmpDestination = production.destinations.find((dest: any) =>
          dest.rtmpUrl && dest.streamKey
        );

        if (rtmpDestination) {
          rtmpEndpoint = `${rtmpDestination.rtmpUrl}/${rtmpDestination.streamKey}`;
          console.log('✅ Found RTMP URL in destination:', rtmpEndpoint);
          console.log('✅ Using destination:', {
            platform: rtmpDestination.platform,
            broadcastId: rtmpDestination.broadcastId,
            streamId: rtmpDestination.streamId,
            rtmpUrl: rtmpDestination.rtmpUrl,
            streamKey: rtmpDestination.streamKey,
            createdAt: rtmpDestination.createdAt
          });
        } else {
          console.log('❌ No destination with RTMP fields found');
          production.destinations.forEach((dest: any, index: number) => {
            console.log(`❌ Destination ${index}:`, {
              platform: dest.platform,
              hasRtmpUrl: !!dest.rtmpUrl,
              hasStreamKey: !!dest.streamKey,
              rtmpUrl: dest.rtmpUrl,
              streamKey: dest.streamKey
            });
          });
        }
      } else if (production.rtmpUrl && production.rtmpStreamKey) {
        // Fallback to legacy fields
        rtmpEndpoint = `${production.rtmpUrl}/${production.rtmpStreamKey}`;
        console.log('✅ Found RTMP URL in legacy fields:', rtmpEndpoint);
      }

      return rtmpEndpoint;
    };

    // If we have production data passed as prop, use it immediately
    if (initialProductionData) {
      const rtmpEndpoint = extractRtmpUrl(initialProductionData);
      if (rtmpEndpoint) {
        setRtmpUrl(rtmpEndpoint);
        console.log('🎯 Using RTMP URL from passed production data:', rtmpEndpoint);
        return;
      }
    }

    // Fallback: fetch production data if not passed or no RTMP URL found
    if (!productionId) {
      console.warn('⚠️ No production ID provided, using fallback RTMP URL');
      setRtmpUrl('rtmp://live.twitch.tv/app/live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax');
      return;
    }

    const fetchProductionData = async () => {
      try {
        console.log('🔄 Fetching production data as fallback...');
        const response = await axios.get(`${API_BASE_URL}/productions/${productionId}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
        });

        const production = response.data;
        setProductionData(production);

        const rtmpEndpoint = extractRtmpUrl(production);
        if (rtmpEndpoint) {
          setRtmpUrl(rtmpEndpoint);
          console.log('🎯 Using RTMP URL from fetched production:', rtmpEndpoint);
        } else {
          console.warn('⚠️ No RTMP destination found in production, using fallback');
          console.warn('⚠️ Production data:', production);
          setRtmpUrl('rtmp://live.twitch.tv/app/live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax');
        }
      } catch (error) {
        console.error('❌ Failed to fetch production data:', error);
        setRtmpUrl('rtmp://live.twitch.tv/app/live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax');
      }
    };

    fetchProductionData();
  }, [productionId, isHost, initialProductionData]);

  // Set RTMP URL when it becomes available and we're connected
  useEffect(() => {
    if (!isHost || !rtmpUrl || !state.isConnected) return;

    const setUrlWhenReady = async () => {
      try {
        console.log('🔧 Setting RTMP URL after it became available:', rtmpUrl);
        console.log('🔧 Media server will stream to:', rtmpUrl);
        await setRTMPUrl(rtmpUrl);
        console.log('✅ RTMP URL configured after becoming available');
      } catch (error) {
        console.error('❌ Failed to set RTMP URL after it became available:', error);
      }
    };

    setUrlWhenReady();
  }, [rtmpUrl, state.isConnected, isHost, setRTMPUrl]);

  // Auto-connect when signaling connection is established (for hosts) or when peer connection is established (for participants)
  useEffect(() => {
    console.log('🔗 RTMPStreaming connection check:', {
      isHost,
      connectionStatus,
      isConnected: state.isConnected,
      hasAttempted: hasAttemptedConnect.current
    });

    const shouldConnect = isHost
      ? (connectionStatus === 'connecting' || connectionStatus === 'connected') // Host: connect when signaling is ready
      : connectionStatus === 'connected'; // Participant: wait for peer connection

    if (shouldConnect && !state.isConnected && !hasAttemptedConnect.current) {
      console.log('🔗 RTMPStreaming: Connection ready, attempting media server connection...');
      hasAttemptedConnect.current = true;

      connect()
        .then(async () => {
          // Set RTMP URL immediately after connection
          if (isHost && rtmpUrl) {
            console.log('🔧 Setting RTMP URL...');
            try {
              await setRTMPUrl(rtmpUrl);
              console.log('✅ RTMP URL configured, waiting for server to request producers...');
            } catch (error) {
              console.error('❌ Failed to set RTMP URL:', error);
            }
          } else if (isHost && !rtmpUrl) {
            console.warn('⚠️ No RTMP URL available yet, waiting...');
          }
        })
        .catch((error) => {
          console.error('❌ RTMPStreaming: Failed to connect to media server:', error);
          hasAttemptedConnect.current = false; // Allow retry on error
        });
    }
  }, [isHost, connectionStatus, state.isConnected, connect, setRTMPUrl]);

  // Listen for server request to create producers for FFmpeg pre-start
  useEffect(() => {
    if (!state.isConnected) return;

    const handleRtmpReadyForProducers = async () => {
      console.log('📡 Server requesting producers for FFmpeg pre-start...', {
        isHost,
        isProducing: state.isProducing,
        hasCanvasStream: !!canvasStream
      });

      if (!isHost) {
        console.log('⚠️ Not a host, ignoring producer request');
        return;
      }

      if (state.isProducing) {
        console.log('⚠️ Already producing, ignoring producer request');
        return;
      }

      if (!canvasStream) {
        console.log('⚠️ Canvas stream not ready for producer creation, will retry when available');
        setServerRequestedProducers(true); // Remember that server requested producers
        return;
      }

      try {
        console.log('🎬 Creating producers for FFmpeg pre-start...');
        await startProducing(canvasStream);
        console.log('✅ Producers created for FFmpeg pre-start');
        setServerRequestedProducers(false); // Reset flag after successful creation
      } catch (error) {
        console.error('❌ Failed to create producers for pre-start:', error);
      }
    };

    // Access the socket from the media server hook
    const mediaServerSocket = state.socket;
    if (mediaServerSocket) {
      console.log('📡 Setting up rtmp-ready-for-producers listener');
      mediaServerSocket.on('rtmp-ready-for-producers', handleRtmpReadyForProducers);

      return () => {
        mediaServerSocket.off('rtmp-ready-for-producers', handleRtmpReadyForProducers);
      };
    } else {
      console.log('⚠️ No socket available for event listening');
    }
  }, [state.isConnected, state.socket, isHost, state.isProducing, canvasStream, startProducing]);

  // Create producers when canvas stream becomes available if server already requested them
  useEffect(() => {
    if (!serverRequestedProducers || !canvasStream || !isHost || state.isProducing) return;

    const createProducers = async () => {
      try {
        console.log('🎬 Canvas stream now available, creating requested producers...');
        await startProducing(canvasStream);
        console.log('✅ Producers created after canvas stream became available');
        setServerRequestedProducers(false);
      } catch (error) {
        console.error('❌ Failed to create producers when canvas stream became available:', error);
      }
    };

    createProducers();
  }, [serverRequestedProducers, canvasStream, isHost, state.isProducing, startProducing]);


  // Clear preparing state when streaming actually starts
  useEffect(() => {
    if (state.isStreaming && isPreparing) {
      console.log('✅ Streaming confirmed, clearing preparing state');
      setIsPreparing(false);
    }
  }, [state.isStreaming, isPreparing]);

  // Clear track-ended errors when canvas stream becomes available
  useEffect(() => {
    if (canvasStream && canvasStream.getTracks().length > 0 &&
        canvasStream.getTracks().every(track => track.readyState === 'live') &&
        state.error && state.error.toLowerCase().includes('track ended')) {
      console.log('✅ Canvas stream restored, clearing track ended error');
      // Clear the error by calling a method that resets error state
      // This will be handled by the media server hook's internal logic
    }
  }, [canvasStream, state.error]);

  // Notify parent of streaming status changes
  useEffect(() => {
    if (onStreamingStatusChange) {
      onStreamingStatusChange(state.isStreaming);
    }
  }, [state.isStreaming, onStreamingStatusChange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 RTMPStreaming component unmounting, disconnecting from media server...');
      disconnect().catch(console.error);
    };
  }, [disconnect]);

  const handleStartStreaming = useCallback(async () => {
    if (isStarting || isPreparing) return; // Prevent double-clicks

    setIsStarting(true);
    setIsPreparing(true);

    try {
      // Connect first if not connected
      if (!state.isConnected) {
        console.log('🔗 Connecting to media server...');
        await connect();
      }

      // Check canvas stream status
      if (!canvasStream) {
        throw new Error('Canvas stream is not available. Please make sure the video compositor is running.');
      }

      // Check if canvas stream tracks are valid
      const tracks = canvasStream.getTracks();
      const hasEndedTracks = tracks.some(track => track.readyState === 'ended');

      if (hasEndedTracks) {
        console.log('⚠️ Canvas stream has ended tracks, restarting...');
        if (onRestartCanvasStream) {
          setIsRestartingCanvas(true);
          onRestartCanvasStream();

          // Wait for the new stream with polling
          let attempts = 0;
          const maxAttempts = 20; // 2 seconds max
          while (attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            if (canvasStream && canvasStream.getTracks().length > 0 &&
                canvasStream.getTracks().every(track => track.readyState === 'live')) {
              console.log('✅ Fresh canvas stream ready');
              break;
            }
          }

          setIsRestartingCanvas(false);

          if (!canvasStream || canvasStream.getTracks().some(track => track.readyState === 'ended')) {
            throw new Error('Unable to get fresh canvas stream. Please refresh the page.');
          }
        } else {
          throw new Error('Canvas stream has ended tracks. Please refresh the page.');
        }
      }

      console.log('✅ Canvas stream is ready with', tracks.length, 'live tracks');

      // Producers should already be created from pre-start, just trigger streaming
      if (!state.isProducing) {
        console.log('⚠️ Producers not created yet, creating now...');
        await startProducing(canvasStream);
      } else {
        console.log('✅ Producers already created, starting RTMP streaming...');
      }

      // Trigger RTMP streaming start (will resume consumers)
      await startRTMPStream();

      // Since we called startRTMPStream() and it succeeded, wait for RTMP to reach YouTube
      console.log('✅ RTMP streaming initiated successfully');
      console.log('🔍 About to check for YouTube destinations...');
      console.log('🔍 Full production data:', productionData);

      // Start YouTube broadcast if we have YouTube destinations
      if (productionData?.destinations) {
        console.log('🔍 Production destinations found:', productionData.destinations);
        const youtubeDestinations = productionData.destinations.filter((dest: any) =>
          dest.platform === 'youtube' && dest.broadcastId
        );
        console.log('🔍 YouTube destinations with broadcast IDs:', youtubeDestinations);

        // TODO: Enable this when Google OAuth app is approved and published (not in testing mode)
        // This will automatically transition YouTube broadcasts to "live" status
        /*
        if (youtubeDestinations.length > 0) {
          console.log('⏳ Waiting 1 second for RTMP stream to reach YouTube...');
          setTimeout(async () => {
            for (const dest of youtubeDestinations) {
              try {
                console.log('📺 Starting YouTube broadcast (going live):', dest.broadcastId);
                await axios.post(`${API_BASE_URL}/youtube/broadcasts/${dest.broadcastId}/start`, {}, {
                  headers: {
                    Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
                  },
                });
                console.log('✅ YouTube broadcast is now live:', dest.broadcastId);
              } catch (error: any) {
                console.error('❌ Failed to start YouTube broadcast:', dest.broadcastId, error);
                if (error.response?.data?.error?.message?.includes('inactive')) {
                  console.log('💡 Stream may need more time to reach YouTube. Try starting the broadcast manually in YouTube Studio.');
                }
              }
            }
          }, 1000); // 1 second delay
        }
        */
      } else {
        console.log('🔍 No production data or destinations found for YouTube broadcast start');
      }
    } catch (error) {
      console.error('Failed to start streaming:', error);
    } finally {
      setIsStarting(false);
      setIsRestartingCanvas(false);
      setIsPreparing(false);
    }
  }, [connect, startProducing, canvasStream, state.isConnected, state.isProducing, state.isStreaming, onRestartCanvasStream, isStarting, isPreparing]);

  const handleStopStreaming = useCallback(async () => {
    if (isStopping) return; // Prevent double-clicks

    setIsStopping(true);
    try {
      // Stop RTMP streaming (pauses consumers, keeps FFmpeg and producers alive)
      if (state.isStreaming) {
        console.log('⏹️ Stopping RTMP stream...');
        await stopRTMPStream();
      }

      // Note: We don't stop producers or canvas stream - they stay alive for instant restart
      console.log('✅ Streaming paused, producers and canvas stream preserved for instant restart');
    } catch (error) {
      console.error('Failed to stop streaming:', error);
    } finally {
      setIsStopping(false);
    }
  }, [stopRTMPStream, state.isStreaming, isStopping]);

  if (!isHost) {
    return null; // Only hosts can control RTMP streaming
  }

  const isStreaming = state.isStreaming;

  return (
    <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
      <h3 className="text-lg font-bold text-white mb-4">RTMP Streaming</h3>

      {/* Streaming Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-3">
          <div
            className={`w-4 h-4 rounded-full ${
              isStreaming ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
            }`}
          />
          <span className="text-white font-medium">
            {isStreaming ? 'Streaming' : 'Not Streaming'}
          </span>
        </div>
      </div>

      {/* Tab Backgrounding Warning - Show when tab is hidden and streaming */}
      {!isTabVisible && isStreaming && (
        <div className="mb-4 p-3 border rounded-lg bg-orange-500/20 border-orange-500/50 text-orange-300">
          <strong>⚠️ Tab Backgrounded:</strong> To maintain stream quality, please keep this tab active and visible.
          <div className="mt-2 text-sm text-orange-400">
            📱 Browsers throttle video/audio processing when tabs are in the background, which may affect your stream.
          </div>
        </div>
      )}

      {/* Error Display - Hide track ended errors during canvas restart */}
      {state.error && !isRestartingCanvas && !state.error.toLowerCase().includes('track ended') && (
        <div className={`mb-4 p-3 border rounded-lg ${
          state.connectionIssue
            ? 'bg-yellow-500/20 border-yellow-500/50 text-yellow-300'
            : 'bg-red-500/20 border-red-500/50 text-red-300'
        }`}>
          <strong>{state.connectionIssue ? 'Connection Issue:' : 'Error:'}</strong> {state.error}
          {state.connectionIssue && !isTabVisible && (
            <div className="mt-2 text-sm text-yellow-400">
              📱 Keep this tab active for optimal streaming. Browsers throttle performance when tabs are backgrounded.
            </div>
          )}
          {state.connectionIssue && isTabVisible && wasTabBackgrounded && (
            <div className="mt-2 text-sm text-yellow-400">
              📱 Tab is now active. If connection issues persist, check your internet connection.
            </div>
          )}
          {state.connectionIssue && isTabVisible && !wasTabBackgrounded && (
            <div className="mt-2 text-sm text-yellow-400">
              💡 Check your internet connection. The stream will automatically recover when connection is restored.
            </div>
          )}
        </div>
      )}

      {/* Stream Controls */}
      <div className="space-y-3">
        {!isStreaming ? (
          <button
            onClick={handleStartStreaming}
            disabled={isStarting || isRestartingCanvas || isPreparing}
            className="w-full px-4 py-3 bg-gray-700/50 border border-red-500/30 text-red-400 rounded-lg hover:bg-gray-600/50 hover:border-red-500/50 focus:outline-none focus:ring-2 focus:ring-red-500/20 disabled:bg-gray-700/30 disabled:border-gray-600/30 disabled:text-gray-500 disabled:cursor-not-allowed font-medium transition-all flex items-center justify-center space-x-2"
          >
            {isPreparing || isStarting || isRestartingCanvas ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>
                  {isRestartingCanvas ? 'Restarting Canvas...' :
                   isPreparing ? 'Preparing...' : 'Starting...'}
                </span>
              </>
            ) : (
              <>
                <span>🔴</span>
                <span>Start Streaming</span>
              </>
            )}
          </button>
        ) : (
          <button
            onClick={handleStopStreaming}
            disabled={isStopping}
            className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/30 text-gray-300 rounded-lg hover:bg-gray-600/50 hover:border-gray-500/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-gray-500/20 font-medium transition-all flex items-center justify-center space-x-2"
          >
            {isStopping ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Stopping...</span>
              </>
            ) : (
              <>
                <span>⏹️</span>
                <span>Stop Streaming</span>
              </>
            )}
          </button>
        )}

        {/* Show warning only if no canvas stream or all tracks are ended, and not restarting */}
        {(!canvasStream || (canvasStream && canvasStream.getTracks().length === 0) ||
          (canvasStream && canvasStream.getTracks().every(track => track.readyState === 'ended'))) &&
         !isRestartingCanvas && (
          <div className="text-sm text-amber-300 bg-amber-500/20 p-3 rounded-lg">
            ⚠️ No video composite available. Make sure the video compositor is running.
          </div>
        )}

        {/* Show status messages */}
        {isPreparing && !isRestartingCanvas && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            ⚙️ Setting up streaming pipeline...
          </div>
        )}

        {isRestartingCanvas && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            🔄 Restarting video composite...
          </div>
        )}

        {!state.isConnected && !isRestartingCanvas && !isPreparing && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            🔗 Connecting to media server...
          </div>
        )}
      </div>

      {/* RTMP Configuration Info */}
      <div className="mt-4 p-3 bg-blue-500/20 rounded-lg">
        <div className="text-sm font-medium text-blue-300 mb-1">RTMP Configuration</div>
        <div className="text-xs text-blue-400 space-y-1">
          {productionData?.destinations && productionData.destinations.length > 0 ? (
            productionData.destinations.map((destination: any, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                {destination.platform === 'youtube' && (
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <span>📺 YouTube:</span>
                      {destination.broadcastId ? (
                        <a
                          href={`https://www.youtube.com/watch?v=${destination.broadcastId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-300 hover:text-blue-200 underline"
                        >
                          Watch Stream
                        </a>
                      ) : (
                        <span>Setting up YouTube stream...</span>
                      )}
                    </div>
                    {destination.broadcastId && (
                      <div className="flex items-center space-x-2 ml-6">
                        <span>🎛️ Studio:</span>
                        <a
                          href={`https://studio.youtube.com/video/${destination.broadcastId}/livestreaming`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-300 hover:text-blue-200 underline"
                        >
                          Manage Stream
                        </a>
                      </div>
                    )}
                  </div>
                )}
                {destination.platform === 'facebook' && (
                  <>
                    <span>📘 Facebook:</span>
                    <span>Live stream (link available after going live)</span>
                  </>
                )}
              </div>
            ))
          ) : (
            <div>No streaming destinations configured</div>
          )}
        </div>
      </div>
    </div>
  );
};
