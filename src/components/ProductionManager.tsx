import React, { useState, useEffect, useRef } from 'react';
import { Video, Users, Plus, ArrowRight, LogOut, User, Calendar, Play, Clock, Edit, Trash2, X, Check, ChevronDown, Link, Lock } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { LinkedPlatformsDialog } from './LinkedPlatformsDialog';
import { PasswordChangeDialog } from './PasswordChangeDialog';
import { StreamingDestinations } from './StreamingDestinations';
import axios from 'axios';

interface ProductionManagerProps {
  onJoinAsHost: (productionId: string, eventName?: string, productionData?: any) => void;
}

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface StreamingDestination {
  platform: 'youtube' | 'facebook';
  channelId: string;
  channelName?: string;
  privacy?: 'public' | 'unlisted' | 'private';
}

export const ProductionManager: React.FC<ProductionManagerProps> = ({
  onJoinAsHost,
}) => {
  const { user, logout } = useAuth();
  const [activeProduction, setActiveProduction] = useState<any>(null);
  const [scheduledEvents, setScheduledEvents] = useState<any[]>([]);
  const [finishedProductions, setFinishedProductions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showEventModal, setShowEventModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState<any>(null);
  const [eventName, setEventName] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [eventScheduledTime, setEventScheduledTime] = useState('');
  const [eventPrivacy, setEventPrivacy] = useState<'public' | 'unlisted' | 'private'>('unlisted');
  const [eventDestinations, setEventDestinations] = useState<StreamingDestination[]>([]);
  const [isCreatingEvent, setIsCreatingEvent] = useState(false);
  const [isGoingLive, setIsGoingLive] = useState<string | null>(null);
  const [isFinishingProduction, setIsFinishingProduction] = useState(false);
  const [activeTab, setActiveTab] = useState<'scheduled' | 'history'>('scheduled');
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showLinkedPlatformsDialog, setShowLinkedPlatformsDialog] = useState(false);
  const [showPasswordChangeDialog, setShowPasswordChangeDialog] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (user) {
      fetchUserData();
    }
  }, [user]);

  // Handle clicks outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowProfileDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch active production (includes both live and scheduled)
      const productionResponse = await axios.get(`${API_BASE_URL}/productions/my-active`);
      const activeProduction = productionResponse.data;
      setActiveProduction(activeProduction);

      // Fetch user's scheduled events (only scheduled status)
      const scheduledResponse = await axios.get(`${API_BASE_URL}/productions/my?status=scheduled`);
      const allScheduledEvents = scheduledResponse.data;

      // Filter out the active production from scheduled events to avoid duplication
      const filteredScheduledEvents = activeProduction
        ? allScheduledEvents.filter((event: any) => event._id !== activeProduction._id)
        : allScheduledEvents;
      setScheduledEvents(filteredScheduledEvents);

      // Fetch finished productions (completed productions)
      const finishedResponse = await axios.get(`${API_BASE_URL}/productions/my?status=completed`);
      setFinishedProductions(finishedResponse.data);
    } catch (err: any) {
      // No active production or events is fine
      if (err.response?.status !== 404) {
        console.error('Error fetching user data:', err);
      }
    } finally {
      setLoading(false);
    }
  };



  const handleEnterProduction = () => {
    if (activeProduction) {
      // For entering existing production, we don't have the full production data
      onJoinAsHost(activeProduction.productionId, undefined, null);
    }
  };

  const handleFinishProduction = async () => {
    if (!activeProduction) return;

    setIsFinishingProduction(true);
    try {
      await axios.post(`${API_BASE_URL}/productions/${activeProduction.productionId}/complete`);
      await fetchUserData(); // Refresh data
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to finish production');
    } finally {
      setIsFinishingProduction(false);
    }
  };

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setEventName('');
    setEventDescription('');
    setEventScheduledTime('');
    setEventPrivacy('unlisted');
    setEventDestinations([]);
    setShowEventModal(true);
  };

  const handleEditEvent = (event: any) => {
    setEditingEvent(event);
    setEventName(event.name);
    setEventDescription(event.description);
    setEventScheduledTime(new Date(event.scheduledStartTime).toISOString().slice(0, 16));
    setEventPrivacy(event.youtubePrivacy || 'unlisted');
    setEventDestinations(event.destinations || []);
    setShowEventModal(true);
  };

  const handleSaveEvent = async (goLiveNow = false) => {
    if (!eventName.trim()) {
      setError('Please enter an event name');
      return;
    }

    setIsCreatingEvent(true);
    setError('');

    try {
      const productionData = {
        name: eventName.trim(),
        description: eventDescription.trim(),
        scheduledStartTime: goLiveNow ? undefined : new Date(eventScheduledTime).toISOString(),
        youtubePrivacy: eventPrivacy,
        destinations: eventDestinations,
      };

      let productionId;
      let createdProduction;
      if (editingEvent) {
        await axios.patch(`${API_BASE_URL}/productions/${editingEvent._id}`, productionData);
        productionId = editingEvent._id;
      } else {
        // Choose endpoint based on whether destinations are provided
        const endpoint = eventDestinations.length > 0
          ? `${API_BASE_URL}/productions/with-destinations`
          : `${API_BASE_URL}/productions`;

        const response = await axios.post(endpoint, productionData, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
        });

        createdProduction = response.data.production || response.data;
        productionId = createdProduction._id;
        console.log('📋 Created production:', createdProduction);

        if (eventDestinations.length > 0 && response.data.createdDestinations) {
          console.log('🎯 Created destinations:', response.data.createdDestinations);
        }
      }

      setShowEventModal(false);

      if (goLiveNow) {
        // For "Go Live Now", use the created production data directly
        console.log('🎬 Go Live Now with production:', productionId);

        // Use the production ID from creation and the event name from the form
        const prodId = createdProduction?.productionId || productionId;
        const prodName = createdProduction?.name || eventName;

        console.log('🎬 Calling onJoinAsHost with:', { prodId, prodName, productionData: createdProduction });
        onJoinAsHost(prodId, prodName, createdProduction);
      } else {
        await fetchUserData(); // Refresh productions list
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save event');
    } finally {
      setIsCreatingEvent(false);
    }
  };

  const handleDeleteEvent = async (productionId: string) => {
    if (!confirm('Are you sure you want to delete this event?')) return;

    try {
      await axios.delete(`${API_BASE_URL}/productions/${productionId}`);
      await fetchUserData(); // Refresh productions list
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete event');
    }
  };

  const handleGoLive = async (productionId: string) => {
    setIsGoingLive(productionId);
    setError('');

    try {
      const response = await axios.post(`${API_BASE_URL}/productions/${productionId}/go-live`);
      const result = response.data;

      // Immediately redirect to Production Center
      const productionIdToJoin = result.productionId;
      const eventName = result.name;
      // For go-live, we don't have the full production data, so pass null
      onJoinAsHost(productionIdToJoin, eventName, null);
    } catch (err: any) {
      console.error('Go live error:', err);
      setError(err.response?.data?.message || 'Failed to go live');
      setIsGoingLive(null);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="bg-orange-500/20 p-3 rounded-lg">
              <User className="w-8 h-8 text-orange-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">{user?.name}</h2>
              <p className="text-gray-400 text-sm">{user?.email}</p>
            </div>
          </div>
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
            >
              <User className="w-5 h-5" />
              <span>Profile</span>
              <ChevronDown className={`w-4 h-4 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showProfileDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      setShowLinkedPlatformsDialog(true);
                      setShowProfileDropdown(false);
                    }}
                    className="w-full text-left px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors flex items-center space-x-2"
                  >
                    <Link className="w-4 h-4" />
                    <span>Linked Platforms</span>
                  </button>
                  <button
                    onClick={() => {
                      setShowPasswordChangeDialog(true);
                      setShowProfileDropdown(false);
                    }}
                    className="w-full text-left px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors flex items-center space-x-2"
                  >
                    <Lock className="w-4 h-4" />
                    <span>Change Password</span>
                  </button>
                  <hr className="border-gray-600 my-1" />
                  <button
                    onClick={() => {
                      logout();
                      setShowProfileDropdown(false);
                    }}
                    className="w-full text-left px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors flex items-center space-x-2"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Welcome Message */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            Welcome to switcher<span className="text-orange-500">.ai</span>
          </h1>
          <p className="text-lg text-gray-400 max-w-2xl mx-auto">
            {activeProduction
              ? "You have an active production ready to go live"
              : "Create a new event or schedule a production to get started"
            }
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6 text-red-400 text-center">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center text-gray-400">Loading...</div>
        ) : (
          <>
            {/* Active/Upcoming Production Section */}
            {activeProduction && (
              <div className="mb-4">
                <div className={`backdrop-blur-lg rounded-2xl p-4 ${
                  activeProduction.status === 'live'
                    ? 'bg-green-500/20 border border-green-500/50'
                    : 'bg-orange-500/20 border border-orange-500/50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-3 rounded-lg mr-4 ${
                        activeProduction.status === 'live'
                          ? 'bg-green-500/20'
                          : 'bg-orange-500/20'
                      }`}>
                        {activeProduction.status === 'live' ? (
                          <Play className="w-8 h-8 text-green-400" />
                        ) : (
                          <Clock className="w-8 h-8 text-orange-400" />
                        )}
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">
                          {activeProduction.status === 'live' ? 'Active Production' : 'Upcoming Production'}
                        </h3>
                        <p className="text-gray-300">{activeProduction.name}</p>
                        <p className={`text-sm ${
                          activeProduction.status === 'live'
                            ? 'text-green-400'
                            : 'text-orange-400'
                        }`}>
                          {activeProduction.status === 'live'
                            ? 'Ready to go live'
                            : `Scheduled for ${new Date(activeProduction.scheduledStartTime).toLocaleDateString()} at ${new Date(activeProduction.scheduledStartTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
                          }
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-3">
                      {activeProduction.status === 'live' ? (
                        <>
                          <button
                            onClick={handleEnterProduction}
                            className="bg-gray-700/50 border border-green-500/30 hover:bg-gray-600/50 hover:border-green-500/50 text-green-400 hover:text-green-300 px-6 py-3 rounded-lg font-semibold transition-all flex items-center space-x-2"
                          >
                            <Video className="w-5 h-5" />
                            <span>Enter Production</span>
                          </button>
                          <button
                            onClick={handleFinishProduction}
                            disabled={isFinishingProduction}
                            className="bg-gray-700/50 border border-red-500/30 hover:bg-gray-600/50 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {isFinishingProduction ? (
                              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <X className="w-5 h-5" />
                            )}
                            <span>Finish Production</span>
                          </button>
                        </>
                      ) : (
                        <>
                          <button
                            onClick={() => handleGoLive(activeProduction._id)}
                            disabled={isGoingLive === activeProduction._id}
                            className="bg-gray-700/50 border border-green-500/30 hover:bg-gray-600/50 hover:border-green-500/50 text-green-400 hover:text-green-300 px-6 py-3 rounded-lg font-semibold transition-all flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {isGoingLive === activeProduction._id ? (
                              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <Play className="w-5 h-5" />
                            )}
                            <span>Go Live</span>
                          </button>
                          <button
                            onClick={() => handleEditEvent(activeProduction)}
                            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center space-x-2"
                          >
                            <Edit className="w-5 h-5" />
                            <span>Edit Event</span>
                          </button>
                          <button
                            onClick={() => handleDeleteEvent(activeProduction._id)}
                            className="bg-gray-700/50 border border-red-500/30 hover:bg-gray-600/50 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all flex items-center space-x-2"
                          >
                            <Trash2 className="w-5 h-5" />
                            <span>Cancel Event</span>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Events & Productions Section */}
            <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-1">Events & Productions</h2>
                  <p className="text-gray-400 text-sm">Manage your events and view production history</p>
                </div>
                {activeTab === 'scheduled' && (
                  <button
                    onClick={handleCreateEvent}
                    className="bg-gray-700/50 border border-gray-600/30 hover:bg-gray-600/50 hover:border-gray-500/50 text-white hover:text-gray-100 px-6 py-3 rounded-lg font-semibold transition-all flex items-center space-x-2"
                  >
                    <Plus className="w-5 h-5" />
                    <span>Create Event</span>
                  </button>
                )}
              </div>

              {/* Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-700/50 rounded-xl p-1">
                <button
                  onClick={() => setActiveTab('scheduled')}
                  className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all ${
                    activeTab === 'scheduled'
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-600/50'
                  }`}
                >
                  Scheduled Events ({scheduledEvents.length})
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all ${
                    activeTab === 'history'
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-600/50'
                  }`}
                >
                  Production History ({finishedProductions.length})
                </button>
              </div>

              {/* Content */}
              {activeTab === 'scheduled' && (
                <div>
                  {scheduledEvents.length === 0 ? (
                    <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-4 text-center">
                      <Calendar className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                      <h3 className="text-lg font-semibold text-white mb-1">No Events Scheduled</h3>
                      <p className="text-gray-400 mb-4 text-sm">Create your first event to start planning your live streams</p>
                      <button
                        onClick={handleCreateEvent}
                        className="bg-gray-700/50 border border-gray-600/30 hover:bg-gray-600/50 hover:border-gray-500/50 text-white hover:text-gray-100 px-6 py-3 rounded-lg font-semibold transition-all flex items-center space-x-2 mx-auto"
                      >
                        <Plus className="w-5 h-5" />
                        <span>Create Event</span>
                      </button>
                    </div>
                  ) : (
                    <div className="grid gap-3">
                      {scheduledEvents.map((event) => (
                        <div key={event._id} className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-4 hover:bg-gray-800 transition-all duration-300">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <Calendar className="w-5 h-5 text-orange-500" />
                                <h3 className="text-xl font-semibold text-white">{event.name}</h3>
                                <span className="px-3 py-1 rounded-full text-xs font-medium bg-orange-500/20 text-orange-400">
                                  scheduled
                                </span>
                              </div>
                              {event.description && (
                                <p className="text-gray-300 mb-3">{event.description}</p>
                              )}
                              <div className="flex items-center space-x-4 text-sm text-gray-400">
                                <div className="flex items-center space-x-1">
                                  <Clock className="w-4 h-4" />
                                  <span>
                                    {new Date(event.scheduledStartTime).toLocaleDateString()} at{' '}
                                    {new Date(event.scheduledStartTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Users className="w-4 h-4" />
                                  <span className="font-mono">
                                    {event.productionId || 'Generating...'}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-3">
                              <button
                                onClick={() => handleGoLive(event._id)}
                                disabled={isGoingLive === event._id}
                                className="bg-gray-700/50 border border-green-500/30 hover:bg-gray-600/50 hover:border-green-500/50 text-green-400 hover:text-green-300 px-4 py-2 rounded-lg font-semibold transition-all flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {isGoingLive === event._id ? (
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <Play className="w-4 h-4" />
                                )}
                                <span>Go Live</span>
                              </button>
                              <button
                                onClick={() => handleEditEvent(event)}
                                className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteEvent(event._id)}
                                className="bg-gray-700/50 border border-red-500/30 hover:bg-gray-600/50 hover:border-red-500/50 text-red-400 hover:text-red-300 p-2 rounded-lg transition-all"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'history' && (
                <div>
                  {finishedProductions.length === 0 ? (
                    <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-4 text-center">
                      <Video className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                      <h3 className="text-lg font-semibold text-white mb-1">No Productions Yet</h3>
                      <p className="text-gray-400 text-sm">Your completed productions will appear here</p>
                    </div>
                  ) : (
                    <div className="grid gap-3">
                      {finishedProductions.map((production) => (
                        <div key={production._id} className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <Video className="w-5 h-5 text-green-500" />
                                <h3 className="text-xl font-semibold text-white">{production.name}</h3>
                                <span className="px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400">
                                  completed
                                </span>
                              </div>
                              {production.description && (
                                <p className="text-gray-300 mb-3">{production.description}</p>
                              )}
                              <div className="flex items-center space-x-4 text-sm text-gray-400">
                                <div className="flex items-center space-x-1">
                                  <Clock className="w-4 h-4" />
                                  <span>
                                    Completed: {new Date(production.completedAt || production.updatedAt).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Users className="w-4 h-4" />
                                  <span className="font-mono">{production.productionId}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Event Modal */}
      {showEventModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 border border-gray-700 rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">
                {editingEvent ? 'Edit Event' : 'Create Event'}
              </h3>
              <button
                onClick={() => setShowEventModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Event Name
                </label>
                <input
                  type="text"
                  value={eventName}
                  onChange={(e) => setEventName(e.target.value)}
                  className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  placeholder="Enter event name"
                  maxLength={100}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={eventDescription}
                  onChange={(e) => setEventDescription(e.target.value)}
                  className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 resize-none"
                  placeholder="Enter event description"
                  rows={3}
                  maxLength={500}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Scheduled Start Time <span className="text-gray-500">(Optional for "Go Live Now")</span>
                </label>
                <input
                  type="datetime-local"
                  value={eventScheduledTime}
                  onChange={(e) => setEventScheduledTime(e.target.value)}
                  className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  min={new Date().toISOString().slice(0, 16)}
                />
              </div>

              {/* Streaming Destinations */}
              <div>
                <StreamingDestinations
                  destinations={eventDestinations}
                  onChange={setEventDestinations}
                  privacy={eventPrivacy}
                  onPrivacyChange={setEventPrivacy}
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-8">
              <button
                onClick={() => setShowEventModal(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-xl font-semibold transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleSaveEvent(false)}
                disabled={isCreatingEvent || !eventName.trim() || (!editingEvent && !eventScheduledTime)}
                className="flex-1 bg-gray-700/50 border border-gray-600/30 hover:bg-gray-600/50 hover:border-gray-500/50 text-white py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCreatingEvent ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <>
                    <Check className="w-5 h-5" />
                    <span>{editingEvent ? 'Update Event' : 'Create Event'}</span>
                  </>
                )}
              </button>
              {!editingEvent && (
                <button
                  onClick={() => handleSaveEvent(true)}
                  disabled={isCreatingEvent || !eventName.trim()}
                  className="flex-1 bg-gray-700/50 border border-green-500/30 hover:bg-gray-600/50 hover:border-green-500/50 text-green-400 hover:text-green-300 py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isCreatingEvent ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      <Play className="w-5 h-5" />
                      <span>Go Live Now</span>
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Linked Platforms Dialog */}
      <LinkedPlatformsDialog
        isOpen={showLinkedPlatformsDialog}
        onClose={() => setShowLinkedPlatformsDialog(false)}
      />

      {/* Password Change Dialog */}
      <PasswordChangeDialog
        isOpen={showPasswordChangeDialog}
        onClose={() => setShowPasswordChangeDialog(false)}
        onSuccess={() => {
          // Refresh user data after password change
          if (user) {
            fetchUserData();
          }
        }}
      />
    </div>
  );
};
