import React from 'react';
import { LayoutMode } from '../webgl/layoutUtils';

interface StatusOverlayProps {
  layoutMode: LayoutMode;
  peerCount: number;
  hasLocalStream: boolean;
  showOverlays?: boolean;
  isTabVisible?: boolean;
  showTabWarning?: boolean;
}

export const StatusOverlay: React.FC<StatusOverlayProps> = ({
  layoutMode,
  peerCount,
  hasLocalStream,
  showOverlays = true,
  isTabVisible = true,
  showTabWarning = false
}) => {
  if (!showOverlays) {
    return null;
  }

  return (
    <div className="absolute bottom-4 left-4 space-y-2">
      {/* Main status overlay */}
      <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg px-3 py-2">
        <span className="text-white text-sm">
          Layout: {layoutMode.toUpperCase()} • {peerCount} participants • {hasLocalStream ? 'Host' : 'No host'}
        </span>
      </div>

      {/* Tab backgrounding warning */}
      {showTabWarning && !isTabVisible && (
        <div className="bg-orange-500/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-orange-400/50">
          <span className="text-white text-sm font-medium">
            ⚠️ Tab backgrounded - Keep active for best performance
          </span>
        </div>
      )}
    </div>
  );
};
