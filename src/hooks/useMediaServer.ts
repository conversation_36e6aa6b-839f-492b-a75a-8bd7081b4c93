import { useRef, useCallback, useState } from 'react';
import { Device, types } from 'mediasoup-client';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '../contexts/AuthContext';

interface MediaServerState {
  isConnected: boolean;
  isProducing: boolean;
  isStreaming: boolean;
  error: string | null;
  connectionIssue: boolean;
  errorFromBackgroundTab: boolean;
  socket?: any; // Expose socket for event listening
}

interface UseMediaServerReturn {
  state: MediaServerState;
  connect: () => Promise<void>;
  startProducing: (stream: MediaStream) => Promise<void>;
  stopProducing: () => Promise<void>;
  setRTMPUrl: (url: string) => Promise<void>;
  startRTMPStream: () => Promise<void>;
  stopRTMPStream: () => Promise<void>;
  disconnect: () => Promise<void>;
}

interface UseMediaServerOptions {
  isTabVisible?: () => boolean;
}

export const useMediaServer = (options: UseMediaServerOptions = {}): UseMediaServerReturn => {
  const { token } = useAuth();
  const { isTabVisible } = options;
  const [state, setState] = useState<MediaServerState>({
    isConnected: false,
    isProducing: false,
    isStreaming: false,
    error: null,
    connectionIssue: false,
    errorFromBackgroundTab: false,
    socket: null,
  });

  const deviceRef = useRef<Device | null>(null);
  const transportRef = useRef<types.Transport | null>(null);
  const producerRef = useRef<types.Producer | null>(null);
  const audioProducerRef = useRef<types.Producer | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const lastPacketCountRef = useRef<number>(0);


  const updateState = useCallback((updates: Partial<MediaServerState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const connect = useCallback(async () => {
    try {
      // Don't connect if already connected
      if (socketRef.current?.connected) {
        console.log('📡 Already connected to media server');
        return;
      }

      // Clean up any existing connection first
      if (socketRef.current) {
        console.log('🧹 Cleaning up existing media server connection...');
        socketRef.current.disconnect();
        socketRef.current = null;
      }

      updateState({ error: null });

      // Get configuration from signaling server
      let mediaServerUrl;
      if (window.location.hostname === 'localhost') {
        // Local development
        mediaServerUrl = 'http://localhost:8081';
      } else {
        // Cloud deployment - fetch config from signaling server
        try {
          const configResponse = await fetch('/api/config');
          const config = await configResponse.json();
          mediaServerUrl = config.mediaServerUrl;
          console.log('📋 Fetched config from server:', config);
        } catch (error) {
          console.error('❌ Failed to fetch config, using fallback');
          mediaServerUrl = 'http://localhost:8081';
        }
      }

      console.log('🔗 Attempting to connect to media server:', mediaServerUrl);

      if (!token) {
        throw new Error('Authentication token required for media server connection');
      }

      const socket = io(mediaServerUrl, {
        transports: ['websocket', 'polling'],
        auth: {
          token: token
        }
      });
      socketRef.current = socket;

      // Expose socket in state for event listening
      updateState({ socket });

      // Listen for streaming status updates
      socket.on('streaming-status', ({ isStreaming }: { isStreaming: boolean }) => {
        console.log('📡 Received streaming status update:', isStreaming ? 'Started' : 'Stopped');
        updateState({ isStreaming });
      });

      socket.on('rtmp-ready-for-producers', (data: any) => {
        console.log('📡 Server ready for producers:', data);
        // This will be handled by the RTMPStreaming component
      });

      socket.on('ffmpeg-ready', (data: any) => {
        console.log('📡 FFmpeg ready:', data);
        // FFmpeg is now pre-started and ready for streaming
      });

      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        socket.on('connect', () => {
          console.log('✅ Connected to media server');
          resolve();
        });
        socket.on('connect_error', (error) => {
          console.error('❌ Connection error:', error);
          reject(error);
        });
        socket.on('disconnect', (reason) => {
          console.log('🔌 Disconnected from media server:', reason);
        });
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      // Create media device
      const device = new Device();
      deviceRef.current = device;

      // Get transport info from server
      const transportInfo = await new Promise<any>((resolve, reject) => {
        socket.emit('get-transport-info');
        socket.on('transport-info', resolve);
        socket.on('error', (error: any) => reject(new Error(error.message)));
        setTimeout(() => reject(new Error('Transport info timeout')), 10000);
      });

      console.log('📋 Received transport info from media server');

      // Load device with router RTP capabilities
      if (!device.loaded) {
        await device.load({
          routerRtpCapabilities: transportInfo.routerRtpCapabilities,
        });
        console.log('✅ Device loaded with RTP capabilities');
      }

      // Create send transport
      const transport = device.createSendTransport({
        id: transportInfo.id,
        iceParameters: transportInfo.iceParameters,
        iceCandidates: transportInfo.iceCandidates,
        dtlsParameters: transportInfo.dtlsParameters,
      });

      console.log('✅ Send transport created');
      transportRef.current = transport;

      // Handle transport connection
      transport.on('connect', async ({ dtlsParameters }, callback, errback) => {
        try {
          socket.emit('connect-transport', { dtlsParameters });
          await new Promise<void>((resolve, reject) => {
            socket.on('transport-connected', resolve);
            socket.on('error', (error: any) => reject(new Error(error.message)));
            setTimeout(() => reject(new Error('Transport connect timeout')), 10000);
          });
          callback();
        } catch (error) {
          errback(error as Error);
        }
      });

      // Handle producer creation
      transport.on('produce', async ({ kind, rtpParameters }, callback, errback) => {
        try {
          const { producerId } = await new Promise<any>((resolve, reject) => {
            socket.emit('create-producer', { kind, rtpParameters });
            socket.on('producer-created', resolve);
            socket.on('error', (error: any) => reject(new Error(error.message)));
            setTimeout(() => reject(new Error('Producer creation timeout')), 10000);
          });
          callback({ id: producerId });
        } catch (error) {
          errback(error as Error);
        }
      });

      updateState({ isConnected: true });
      console.log('✅ Connected to media server');
    } catch (error) {
      console.error('❌ Failed to connect to media server:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to connect to media server',
        isConnected: false
      });
      throw error;
    }
  }, [updateState]);

  const startProducing = useCallback(async (canvasStream: MediaStream) => {
    try {
      if (!deviceRef.current || !transportRef.current) {
        throw new Error('Device or transport not initialized');
      }

      // Prevent multiple simultaneous calls
      if (state.isProducing) {
        console.log('⚠️ Already producing, ignoring duplicate startProducing call');
        return;
      }

      updateState({ error: null });

      // Clean up any existing producers first
      if (audioProducerRef.current) {
        console.log('🧹 Cleaning up existing audio producer');
        audioProducerRef.current.close();
        audioProducerRef.current = null;
      }

      if (producerRef.current) {
        console.log('🧹 Cleaning up existing video producer');
        producerRef.current.close();
        producerRef.current = null;
      }

      // Get microphone access for audio
      let microphoneStream: MediaStream | null = null;
      try {
        microphoneStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: false,  // Disable to preserve audio quality
            autoGainControl: false,   // Disable to preserve dynamic range
            sampleRate: 48000,        // High quality sample rate
            sampleSize: 16,           // 16-bit audio
            channelCount: 2,          // Stereo
          },
          video: false,
        });
        console.log('✅ Microphone access granted');
      } catch (error) {
        console.warn('⚠️ Microphone access denied, proceeding with video only:', error);
      }

      // Create audio producer if microphone is available
      if (microphoneStream) {
        const audioTrack = microphoneStream.getAudioTracks()[0];
        if (audioTrack) {
          console.log('🎵 Creating audio producer...');
          const audioProducer = await transportRef.current.produce({
            track: audioTrack,
            codecOptions: {
              opusStereo: true,
              opusDtx: false,           // Disable DTX to maintain constant bitrate
              opusFec: true,            // Enable forward error correction
              opusMaxPlaybackRate: 48000, // High quality playback
            },
            encodings: [{
              maxBitrate: 128000,       // Request 128kbps from browser
            }],
          });

          audioProducerRef.current = audioProducer;

          audioProducer.on('transportclose', () => {
            console.log('Audio producer transport closed');
          });

          audioProducer.on('trackended', () => {
            console.log('Audio producer track ended');
          });

          console.log('✅ Audio producer created:', audioProducer.id);
        }
      }

      // Create video producer from canvas stream
      const videoTrack = canvasStream.getVideoTracks()[0];
      if (!videoTrack) {
        throw new Error('No video track found in canvas stream');
      }

      const videoProducer = await transportRef.current.produce({
        track: videoTrack,
        encodings: [
          {
            maxBitrate: 5000000, // 5Mbps
            maxFramerate: 30,
          },
        ],
        codecOptions: {
          videoGoogleStartBitrate: 5000, // 5Mbps in kbps
        },
      });

      producerRef.current = videoProducer;

      videoProducer.on('transportclose', () => {
        console.log('Video producer transport closed');
        updateState({ isProducing: false });
      });

      videoProducer.on('trackended', () => {
        console.log('Video producer track ended');
        updateState({ isProducing: false });
      });

      updateState({ isProducing: true });

      console.log('✅ Started producing video stream');
      console.log('📊 Video producer details:', {
        id: videoProducer.id,
        kind: videoProducer.kind,
        paused: videoProducer.paused,
        track: {
          kind: videoTrack.kind,
          enabled: videoTrack.enabled,
          readyState: videoTrack.readyState,
          settings: videoTrack.getSettings?.()
        }
      });

      // Note: RTMP streaming will auto-start on the server side
      // The streaming status will be updated via the 'streaming-status' event

      // Ensure video producer is not paused
      if (videoProducer.paused) {
        console.log('🔄 Video producer was paused, resuming...');
        videoProducer.resume();
        console.log('✅ Video producer resumed, paused:', videoProducer.paused);
      }

      // Monitor video producer stats every 5 seconds and detect connection issues
      const statsInterval = setInterval(async () => {
        try {
          const stats = await videoProducer.getStats();
          const statsArray = Array.from(stats.values());
          console.log('📊 Video producer stats:', statsArray);

          // Check for connection issues (no packets being sent)
          const outboundStats = statsArray.find(stat => stat.type === 'outbound-rtp');
          if (outboundStats && outboundStats.packetsSent !== undefined) {
            const currentPackets = outboundStats.packetsSent;
            const lastPackets = lastPacketCountRef.current;

            if (currentPackets === lastPackets && currentPackets > 0) {
              const tabIsVisible = isTabVisible ? isTabVisible() : true;
              console.log('⚠️ No new packets sent - possible connection issue detected', {
                tabIsVisible,
                isTabVisibleFunction: !!isTabVisible,
                errorFromBackgroundTab: !tabIsVisible
              });
              updateState({
                error: 'Connection issue detected - stream may be interrupted',
                connectionIssue: true,
                errorFromBackgroundTab: !tabIsVisible
              });
            } else if (state.connectionIssue && currentPackets > lastPackets) {
              console.log('✅ Connection restored - packets flowing again');
              updateState({
                error: null,
                connectionIssue: false,
                errorFromBackgroundTab: false
              });
            }

            lastPacketCountRef.current = currentPackets;
          }
        } catch (error) {
          const tabIsVisible = isTabVisible ? isTabVisible() : true;
          console.log('⚠️ Failed to get video producer stats:', error, { tabIsVisible });
          updateState({
            error: 'Connection monitoring failed - possible network issue',
            connectionIssue: true,
            errorFromBackgroundTab: !tabIsVisible
          });
        }
      }, 5000);

      // Clean up stats monitoring when producer is closed
      videoProducer.on('@close', () => {
        clearInterval(statsInterval);
        console.log('🔌 Video producer closed');
      });
    } catch (error) {
      console.error('❌ Failed to start producing:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to start producing',
        isProducing: false
      });
      throw error;
    }
  }, [updateState]);

  const stopProducing = useCallback(async () => {
    try {
      if (audioProducerRef.current) {
        audioProducerRef.current.close();
        audioProducerRef.current = null;
        console.log('⏹️ Stopped producing audio stream');
      }

      if (producerRef.current) {
        producerRef.current.close();
        producerRef.current = null;
        console.log('⏹️ Stopped producing video stream');
      }

      updateState({ isProducing: false });
    } catch (error) {
      console.error('❌ Failed to stop producing:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to stop producing'
      });
    }
  }, [updateState]);

  const startRTMPStream = useCallback(async () => {
    try {
      if (!socketRef.current) {
        throw new Error('Socket not initialized');
      }

      updateState({ error: null });

      // Manually start RTMP streaming
      await new Promise<any>((resolve, reject) => {
        const socket = socketRef.current!;

        const onRtmpStatus = (data: any) => {
          socket.off('rtmp-status', onRtmpStatus);
          socket.off('error', onError);
          resolve(data);
        };

        const onError = (error: any) => {
          socket.off('rtmp-status', onRtmpStatus);
          socket.off('error', onError);
          reject(new Error(error.message));
        };

        socket.on('rtmp-status', onRtmpStatus);
        socket.on('error', onError);
        socket.emit('start-rtmp');

        setTimeout(() => {
          socket.off('rtmp-status', onRtmpStatus);
          socket.off('error', onError);
          reject(new Error('RTMP start timeout'));
        }, 10000);
      });

      updateState({ isStreaming: true });
      console.log('✅ RTMP streaming started manually');
    } catch (error) {
      console.error('❌ Failed to start RTMP stream:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to start RTMP stream'
      });
      throw error;
    }
  }, [updateState]);

  const stopRTMPStream = useCallback(async () => {
    try {
      if (!socketRef.current) {
        throw new Error('Socket not initialized');
      }

      await new Promise<any>((resolve, reject) => {
        const socket = socketRef.current!;

        const onRtmpStatus = (data: any) => {
          socket.off('rtmp-status', onRtmpStatus);
          socket.off('error', onError);
          resolve(data);
        };

        const onError = (error: any) => {
          socket.off('rtmp-status', onRtmpStatus);
          socket.off('error', onError);
          reject(new Error(error.message));
        };

        socket.on('rtmp-status', onRtmpStatus);
        socket.on('error', onError);
        socket.emit('stop-rtmp');

        setTimeout(() => {
          socket.off('rtmp-status', onRtmpStatus);
          socket.off('error', onError);
          reject(new Error('RTMP stop timeout'));
        }, 10000);
      });

      // Don't update isStreaming here - wait for server confirmation via streaming-status event
      console.log('⏹️ RTMP stop request sent, waiting for server confirmation');
    } catch (error) {
      console.error('❌ Failed to stop RTMP stream:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to stop RTMP stream'
      });
      throw error;
    }
  }, [updateState]);

  const setRTMPUrl = useCallback(async (url: string) => {
    try {
      if (!socketRef.current) {
        throw new Error('Socket not initialized');
      }

      updateState({ error: null });

      await new Promise<any>((resolve, reject) => {
        const socket = socketRef.current!;

        const onRtmpUrlSet = (data: any) => {
          socket.off('rtmp-url-set', onRtmpUrlSet);
          socket.off('error', onError);
          resolve(data);
        };

        const onError = (error: any) => {
          socket.off('rtmp-url-set', onRtmpUrlSet);
          socket.off('error', onError);
          reject(new Error(error.message));
        };

        socket.on('rtmp-url-set', onRtmpUrlSet);
        socket.on('error', onError);
        socket.emit('set-rtmp-url', { url });

        setTimeout(() => {
          socket.off('rtmp-url-set', onRtmpUrlSet);
          socket.off('error', onError);
          reject(new Error('RTMP URL set timeout'));
        }, 5000);
      });

      console.log('✅ RTMP URL configured:', url);
    } catch (error) {
      console.error('❌ Failed to set RTMP URL:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to set RTMP URL'
      });
      throw error;
    }
  }, [updateState]);

  const disconnect = useCallback(async () => {
    try {
      if (producerRef.current) {
        producerRef.current.close();
        producerRef.current = null;
      }

      if (transportRef.current) {
        transportRef.current.close();
        transportRef.current = null;
      }

      if (socketRef.current) {
        console.log('🔌 Sending disconnect to media server...');
        socketRef.current.disconnect();
        // Give a small delay to ensure the disconnect message is sent
        await new Promise(resolve => setTimeout(resolve, 100));
        socketRef.current = null;
      }

      deviceRef.current = null;

      updateState({
        isConnected: false,
        isProducing: false,
        isStreaming: false,
        error: null,
      });

      console.log('🔌 Disconnected from media server');
    } catch (error) {
      console.error('❌ Error during disconnect:', error);
    }
  }, [updateState]);

  return {
    state,
    connect,
    startProducing,
    stopProducing,
    setRTMPUrl,
    startRTMPStream,
    stopRTMPStream,
    disconnect,
  };
};
