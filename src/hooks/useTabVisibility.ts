import { useState, useEffect } from 'react';

/**
 * Custom hook to track browser tab visibility using the Page Visibility API
 * Returns whether the tab is currently visible and tracks if it was backgrounded
 */
export const useTabVisibility = () => {
  const [isVisible, setIsVisible] = useState(!document.hidden);
  const [visibilityState, setVisibilityState] = useState(document.visibilityState);
  const [wasBackgrounded, setWasBackgrounded] = useState(false);
  const [showBackgroundWarning, setShowBackgroundWarning] = useState(false);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const newIsVisible = !document.hidden;
      const newVisibilityState = document.visibilityState;

      setIsVisible(newIsVisible);
      setVisibilityState(newVisibilityState);

      if (!newIsVisible) {
        // Tab became hidden
        setWasBackgrounded(true);
        setShowBackgroundWarning(false); // Hide warning while backgrounded
        console.log(`📱 Tab backgrounded: ${newVisibilityState}`);
      } else if (wasBackgrounded) {
        // Tab became visible again after being backgrounded
        setShowBackgroundWarning(true);
        console.log(`📱 Tab restored after being backgrounded: ${newVisibilityState}`);
      }
    };

    // Add event listener for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [wasBackgrounded]);

  const dismissBackgroundWarning = () => {
    setShowBackgroundWarning(false);
    setWasBackgrounded(false);
  };

  return {
    isVisible,
    visibilityState,
    isHidden: !isVisible,
    wasBackgrounded,
    showBackgroundWarning,
    dismissBackgroundWarning
  };
};
