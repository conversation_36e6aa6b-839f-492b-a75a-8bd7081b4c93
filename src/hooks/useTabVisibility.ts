import { useState, useEffect } from 'react';

/**
 * Custom hook to track browser tab visibility using the Page Visibility API
 * Returns whether the tab is currently visible and provides visibility state
 */
export const useTabVisibility = () => {
  const [isVisible, setIsVisible] = useState(!document.hidden);
  const [visibilityState, setVisibilityState] = useState(document.visibilityState);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const newIsVisible = !document.hidden;
      const newVisibilityState = document.visibilityState;
      
      setIsVisible(newIsVisible);
      setVisibilityState(newVisibilityState);
      
      console.log(`📱 Tab visibility changed: ${newVisibilityState} (visible: ${newIsVisible})`);
    };

    // Add event listener for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return {
    isVisible,
    visibilityState,
    isHidden: !isVisible
  };
};
