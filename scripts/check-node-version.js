#!/usr/bin/env node

import semver from 'semver';

const REQUIRED_NODE_VERSION = '>=22.0.0';

function checkNodeVersion() {
  const currentVersion = process.version;

  if (!semver.satisfies(currentVersion, REQUIRED_NODE_VERSION)) {
    console.error('\x1b[31m❌ Node.js version check failed!\x1b[0m');
    console.error(`\x1b[31m   Current: ${currentVersion}, Required: ${REQUIRED_NODE_VERSION}\x1b[0m`);
    console.error('\x1b[33m💡 Upgrade: nvm install 22 && nvm use 22\x1b[0m');
    process.exit(1);
  }

  console.log(`\x1b[32m✅ Node.js ${currentVersion}\x1b[0m`);
}

checkNodeVersion();
