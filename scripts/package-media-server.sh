#!/bin/bash

# Package Media Server for OCI Artifact Registry
# This script creates a versioned tgz package of the media server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Function to get version from package.json
get_version() {
    local version
    if [ -f "media-server/package.json" ]; then
        version=$(node -p "require('./media-server/package.json').version" 2>/dev/null || echo "1.0.0")
        # If version is "latest" or empty, default to "1.0.0"
        if [ -z "$version" ] || [ "$version" = "latest" ] || [ "$version" = "undefined" ]; then
            version="1.0.0"
        fi
    else
        version="1.0.0"
    fi
    echo "$version"
}

# Function to create package
create_package() {
    local version=$1
    local package_name="media-server-${version}.tgz"
    local output_dir="dist"
    
    log_info "Creating package: ${package_name}"
    
    # Create output directory
    mkdir -p "${output_dir}"
    
    # Check if media-server directory exists
    if [ ! -d "media-server" ]; then
        log_error "media-server directory not found. Please run this script from the project root."
        exit 1
    fi
    
    # Create temporary directory for packaging
    local temp_dir=$(mktemp -d)
    local package_dir="${temp_dir}/media-server"
    
    # Copy media server files
    mkdir -p "${package_dir}"
    
    log_info "Copying media server files..."
    
    # Copy essential files
    if [ -f "media-server/server.js" ]; then
        cp "media-server/server.js" "${package_dir}/"
    else
        log_error "server.js not found in media-server directory"
        rm -rf "${temp_dir}"
        exit 1
    fi
    
    # Copy configuration files
    [ -f "media-server/config.js" ] && cp "media-server/config.js" "${package_dir}/"
    [ -f "media-server/config.production.js" ] && cp "media-server/config.production.js" "${package_dir}/"
    [ -f "media-server/package.json" ] && cp "media-server/package.json" "${package_dir}/"
    [ -f "media-server/package-lock.json" ] && cp "media-server/package-lock.json" "${package_dir}/"
    
    # Copy SSL certificates if they exist
    if [ -d "media-server/ssl" ]; then
        cp -r "media-server/ssl" "${package_dir}/"
    fi
    
    # Skip node_modules for ARM64 deployment (they need to be compiled on target)
    log_info "Excluding node_modules from package for ARM64 compilation on target"
    log_info "Dependencies will be installed and compiled on the target ARM64 system"
    
    # Create tarball
    log_info "Creating tarball..."
    cd "${temp_dir}"
    tar -czf "${package_name}" media-server/
    
    # Move to output directory
    mv "${package_name}" "${OLDPWD}/${output_dir}/"
    cd "${OLDPWD}"
    
    # Cleanup
    rm -rf "${temp_dir}"
    
    echo "${output_dir}/${package_name}"
}

# Function to show package info
show_package_info() {
    local package_file=$1
    local version=$2
    
    log_success "Package created successfully!"
    log_info "Package: ${package_file}"
    log_info "Version: ${version}"
    log_info "Size: $(du -h "${package_file}" | cut -f1)"
    
    echo ""
    log_info "To upload to OCI Artifact Registry:"
    echo "1. Deploy the Terraform infrastructure first:"
    echo "   cd terraform/environments/oci-dev"
    echo "   terraform apply"
    echo ""
    echo "2. Use the generated upload script:"
    echo "   terraform output -raw upload_script_path"
    echo "   # Copy the media server package to the media-server directory"
    echo "   # Run the upload script from the media-server directory"
    echo ""
    echo "3. Or manually upload using OCI CLI:"
    echo "   oci artifacts generic artifact upload-by-path \\"
    echo "     --repository-id \$(terraform output -raw artifact_repository_id) \\"
    echo "     --artifact-path \"media-server-${version}.tgz\" \\"
    echo "     --content-body \"${package_file}\" \\"
    echo "     --artifact-version \"${version}\""
}

# Main function
main() {
    local version_override=$1
    
    log_info "Media Server Package Creator"
    
    # Check if we're in the right directory
    if [ ! -d "media-server" ]; then
        log_error "Please run this script from the project root directory (where media-server/ exists)"
        exit 1
    fi
    
    # Get version
    local version
    if [ -n "${version_override}" ]; then
        version="${version_override}"
        log_info "Using provided version: ${version}"
    else
        version=$(get_version)
        log_info "Using version from package.json: ${version}"
    fi
    
    # Create package
    local package_file
    package_file=$(create_package "${version}")
    
    if [ ! -f "${package_file}" ]; then
        log_error "Failed to create package"
        exit 1
    fi
    
    # Show package info
    show_package_info "${package_file}" "${version}"
}

# Show usage if help requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "Usage: $0 [version]"
    echo ""
    echo "Package media server for OCI Artifact Registry deployment"
    echo ""
    echo "Arguments:"
    echo "  version    Optional version override (uses package.json version if not provided)"
    echo ""
    echo "Examples:"
    echo "  $0                # Use version from media-server/package.json"
    echo "  $0 1.2.3          # Use specific version"
    echo ""
    echo "Note: Run this script from the project root directory"
    exit 0
fi

# Run main function
main "$@"
