#!/bin/bash

# Complete OCI Media Server Deployment Workflow
# This script handles packaging, infrastructure deployment, and package upload

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
TERRAFORM_DIR="terraform/environments/oci-dev"
PACKAGE_SCRIPT="scripts/package-media-server.sh"
DIST_DIR="dist"

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -d "media-server" ] || [ ! -f "${PACKAGE_SCRIPT}" ]; then
        log_error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed"
        exit 1
    fi
    
    # Check if OCI CLI is installed
    if ! command -v oci &> /dev/null; then
        log_error "OCI CLI is not installed"
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_error "jq is not installed (required for JSON parsing)"
        exit 1
    fi
    
    # Check if terraform.tfvars exists
    if [ ! -f "${TERRAFORM_DIR}/terraform.tfvars" ]; then
        log_error "terraform.tfvars not found in ${TERRAFORM_DIR}"
        log_info "Please copy terraform.tfvars.example and configure it"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Function to get version from terraform.tfvars
get_version_from_tfvars() {
    local version
    version=$(grep '^package_version' "${TERRAFORM_DIR}/terraform.tfvars" | sed 's/.*= *"\([^"]*\)".*/\1/' | tr -d ' ')

    # If version is "latest" or empty, default to "1.0.0"
    if [ -z "$version" ] || [ "$version" = "latest" ]; then
        version="1.0.0"
    fi

    echo "$version"
}

# Function to package media server
package_media_server() {
    local version=$1
    
    log_info "Packaging media server version ${version}..."
    
    if [ ! -x "${PACKAGE_SCRIPT}" ]; then
        chmod +x "${PACKAGE_SCRIPT}"
    fi
    
    "${PACKAGE_SCRIPT}" "${version}"
    
    local package_file="${DIST_DIR}/media-server-${version}.tgz"
    if [ ! -f "${package_file}" ]; then
        log_error "Package creation failed"
        exit 1
    fi
    
    log_success "Package created: ${package_file}"
    echo "${package_file}"
}

# Function to deploy infrastructure
deploy_infrastructure() {
    log_info "Deploying infrastructure..."
    
    cd "${TERRAFORM_DIR}"

    # Always reinitialize to handle provider changes
    log_info "Initializing Terraform..."
    terraform init -upgrade

    # Plan and apply
    terraform plan -out=tfplan
    terraform apply tfplan
    rm -f tfplan
    
    cd - > /dev/null
    
    log_success "Infrastructure deployed"
}

# Function to create repository and upload package
create_repository_and_upload() {
    local package_file=$1
    local version=$2

    log_info "Creating repository and uploading package..."

    cd "${TERRAFORM_DIR}"

    # Get setup script path
    local setup_script
    if ! setup_script=$(terraform output -raw artifact_registry_info 2>/dev/null | jq -r '.setup_script' 2>/dev/null); then
        log_error "Failed to get setup script path from Terraform output"
        cd - > /dev/null
        exit 1
    fi

    cd - > /dev/null

    # Make setup script executable
    chmod +x "${setup_script}"

    # Create repository
    log_info "Creating artifact repository..."
    if ! "${setup_script}" create; then
        log_error "Failed to create repository"
        exit 1
    fi

    # Upload package
    log_info "Uploading package..."
    if ! "${setup_script}" upload "${package_file}" "${version}"; then
        log_error "Failed to upload package"
        exit 1
    fi

    log_success "Repository created and package uploaded successfully"
}

# Function to redeploy media server
redeploy_media_server() {
    log_info "Redeploying media server to download new package..."
    
    cd "${TERRAFORM_DIR}"
    
    # Force replacement of the media server instance
    terraform apply -replace=module.oci_media_server.oci_core_instance.media_server -auto-approve
    
    cd - > /dev/null
    
    log_success "Media server redeployed"
}

# Function to show deployment info
show_deployment_info() {
    log_info "Getting deployment information..."
    
    cd "${TERRAFORM_DIR}"
    
    local public_ip
    if public_ip=$(terraform output -raw media_server_public_ip 2>/dev/null); then
        log_success "Deployment completed successfully!"
        echo ""
        log_info "Media Server Details:"
        echo "  Public IP: ${public_ip}"
        echo "  URL: https://${public_ip}:8080"
        echo "  SSH: ssh opc@${public_ip}"
        echo ""
        log_info "Useful Commands:"
        echo "  Check service: ssh opc@${public_ip} 'sudo systemctl status media-server'"
        echo "  View logs: ssh opc@${public_ip} 'sudo journalctl -u media-server -f'"
        echo "  Restart service: ssh opc@${public_ip} 'sudo systemctl restart media-server'"
    else
        log_warning "Could not retrieve deployment information"
    fi
    
    cd - > /dev/null
}

# Function to clean up
cleanup() {
    log_info "Cleaning up temporary files..."
    rm -f "${TERRAFORM_DIR}/tfplan"
}

# Main function
main() {
    local action=${1:-"deploy"}
    local version_override=$2
    
    log_info "OCI Media Server Deployment Workflow"
    log_info "Action: ${action}"
    
    case $action in
        "package")
            check_prerequisites
            local version=${version_override:-$(get_version_from_tfvars)}
            package_media_server "${version}"
            ;;
        "infrastructure")
            check_prerequisites
            deploy_infrastructure
            ;;
        "upload")
            check_prerequisites
            local version=${version_override:-$(get_version_from_tfvars)}
            local package_file="${DIST_DIR}/media-server-${version}.tgz"
            if [ ! -f "${package_file}" ]; then
                log_error "Package file not found: ${package_file}"
                log_info "Run './scripts/deploy-oci-media-server.sh package' first"
                exit 1
            fi
            create_repository_and_upload "${package_file}" "${version}"
            ;;
        "redeploy")
            check_prerequisites
            redeploy_media_server
            show_deployment_info
            ;;
        "deploy")
            check_prerequisites
            local version=${version_override:-$(get_version_from_tfvars)}
            log_info "Starting complete deployment for version ${version}"
            
            # Step 1: Package
            local package_file
            package_file=$(package_media_server "${version}")
            
            # Step 2: Deploy infrastructure
            deploy_infrastructure
            
            # Step 3: Create repository and upload package
            create_repository_and_upload "${package_file}" "${version}"
            
            # Step 4: Redeploy media server
            redeploy_media_server
            
            # Step 5: Show info
            show_deployment_info
            ;;
        "destroy")
            log_warning "This will destroy all OCI resources. Are you sure? (y/N)"
            read -r response
            if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
                cd "${TERRAFORM_DIR}"
                terraform destroy
                cd - > /dev/null
                log_success "Resources destroyed"
            else
                log_info "Destroy cancelled"
            fi
            ;;
        *)
            log_error "Unknown action: ${action}"
            echo ""
            echo "Usage: $0 [action] [version]"
            echo ""
            echo "Actions:"
            echo "  deploy        Complete deployment (package + infrastructure + upload + redeploy)"
            echo "  package       Package media server only"
            echo "  infrastructure Deploy infrastructure only"
            echo "  upload        Upload package to artifact registry"
            echo "  redeploy      Redeploy media server instance"
            echo "  destroy       Destroy all resources"
            echo ""
            echo "Examples:"
            echo "  $0 deploy           # Complete deployment using version from terraform.tfvars"
            echo "  $0 deploy 1.2.3     # Complete deployment with specific version"
            echo "  $0 package 1.2.3    # Package specific version"
            echo "  $0 upload           # Upload existing package"
            exit 1
            ;;
    esac
    
    cleanup
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Run main function
main "$@"
