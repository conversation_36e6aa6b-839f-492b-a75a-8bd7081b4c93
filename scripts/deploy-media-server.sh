#!/bin/bash

# 🚀 Simple Media Server Deployment Script
# No Docker required - uses OCI server for compilation

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; exit 1; }

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MEDIA_SERVER_DIR="$PROJECT_ROOT/media-server"
TERRAFORM_DIR="$PROJECT_ROOT/terraform/environments/oci-dev"

# Parse arguments
PACKAGE_AFTER_BUILD="false"
FORCE_REBUILD="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --package)
            PACKAGE_AFTER_BUILD="true"
            shift
            ;;
        --force-rebuild)
            FORCE_REBUILD="true"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --package        Package compiled result for future instant deployments"
            echo "  --force-rebuild  Force rebuild even if no changes detected"
            echo "  --help          Show this help message"
            echo ""
            echo "This script:"
            echo "1. Packages your local media-server code"
            echo "2. Uploads it to OCI Object Storage"
            echo "3. Deploys and compiles on OCI server (8 minutes)"
            echo "4. Optionally packages the compiled result for instant future deployments"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            ;;
    esac
done

# Get version from package.json
get_version() {
    if [ -f "$MEDIA_SERVER_DIR/package.json" ]; then
        node -p "require('$MEDIA_SERVER_DIR/package.json').version" 2>/dev/null || echo "1.0.0"
    else
        echo "1.0.0"
    fi
}

# Check if media-server directory exists
check_media_server() {
    if [ ! -d "$MEDIA_SERVER_DIR" ]; then
        error "Media server directory not found: $MEDIA_SERVER_DIR"
    fi
    
    if [ ! -f "$MEDIA_SERVER_DIR/package.json" ]; then
        error "package.json not found in media server directory"
    fi
    
    success "Media server directory found"
}

# Package the media server source
package_source() {
    local version="$1"
    local package_file="$PROJECT_ROOT/media-server-${version}.tgz"

    log "Packaging media server source..."

    cd "$MEDIA_SERVER_DIR"

    # Create package excluding node_modules and build artifacts
    tar --exclude='node_modules' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        --exclude='coverage' \
        --exclude='dist' \
        -czf "$package_file" .

    if [ -f "$package_file" ]; then
        local size=$(du -h "$package_file" | cut -f1)
        success "Source package created: media-server-${version}.tgz ($size)"
        # Return the package file path without any log output
        return 0
    else
        error "Failed to create source package"
    fi
}

# Upload package to OCI Object Storage
upload_package() {
    local package_file="$1"
    local version="$2"
    
    log "Uploading package to OCI Object Storage..."
    
    if ! command -v oci >/dev/null 2>&1; then
        error "OCI CLI is required but not installed. Please install it first."
    fi
    
    # Upload to the bucket (this will be picked up by the startup script)
    local bucket_name="sai-platform-oci-dev-media-server-repo"
    local object_name="media-server-${version}.tgz"
    
    oci os object put \
        --bucket-name "$bucket_name" \
        --file "$package_file" \
        --name "$object_name" \
        --force || error "Failed to upload package to OCI Object Storage"
    
    success "Package uploaded: $object_name"
    
    # Cleanup local package
    rm -f "$package_file"
}

# Deploy using Terraform
deploy_terraform() {
    log "Deploying to OCI using Terraform..."
    
    if [ ! -d "$TERRAFORM_DIR" ]; then
        error "Terraform directory not found: $TERRAFORM_DIR"
    fi
    
    cd "$TERRAFORM_DIR"
    
    # Apply Terraform configuration
    terraform apply -auto-approve || error "Terraform deployment failed"
    
    success "Terraform deployment completed"
    
    # Get server IP
    local server_ip=$(terraform output -raw media_server_public_ip 2>/dev/null || echo "unknown")
    if [ "$server_ip" != "unknown" ]; then
        log "Server IP: $server_ip"
        log "Server URL: https://$server_ip:8080"
    fi
}

# Monitor deployment progress
monitor_deployment() {
    local server_ip="$1"
    
    if [ "$server_ip" = "unknown" ]; then
        warning "Server IP unknown, cannot monitor deployment"
        return
    fi
    
    log "Monitoring deployment progress..."
    log "This will take about 8 minutes for compilation..."
    
    # Wait a bit for server to start
    sleep 30
    
    # Monitor startup script logs
    ssh -i ~/.ssh/oci_key -o StrictHostKeyChecking=no opc@"$server_ip" \
        'sudo tail -f /var/log/startup-script.log' 2>/dev/null || {
        warning "Could not connect to monitor logs. Server may still be starting."
        log "You can manually check progress with:"
        log "ssh -i ~/.ssh/oci_key opc@$server_ip 'sudo tail -f /var/log/startup-script.log'"
    }
}

# Package compiled result (for future instant deployments)
package_compiled_result() {
    local server_ip="$1"
    local version="$2"
    
    if [ "$server_ip" = "unknown" ]; then
        warning "Server IP unknown, cannot package compiled result"
        return
    fi
    
    log "Packaging compiled result for future instant deployments..."
    
    # Create compiled package on server
    ssh -i ~/.ssh/oci_key -o StrictHostKeyChecking=no opc@"$server_ip" \
        'cd /opt/media-server && sudo tar --exclude="node_modules/.cache" --exclude="*.log" -czf /tmp/media-server-compiled.tgz .' || {
        warning "Failed to create compiled package on server"
        return
    }
    
    # Download compiled package
    local compiled_package="$PROJECT_ROOT/media-server-${version}-compiled.tgz"
    scp -i ~/.ssh/oci_key -o StrictHostKeyChecking=no \
        opc@"$server_ip":/tmp/media-server-compiled.tgz \
        "$compiled_package" || {
        warning "Failed to download compiled package"
        return
    }
    
    if [ -f "$compiled_package" ]; then
        local size=$(du -h "$compiled_package" | cut -f1)
        success "Compiled package downloaded: media-server-${version}-compiled.tgz ($size)"
        
        # Upload to OCI Artifact Registry for future instant deployments
        log "Uploading compiled package to OCI Artifact Registry..."
        oci artifacts generic artifact upload-by-path \
            --repository-id "ocid1.artifactrepository.oc1.iad.0.amaaaaaa2fhu3uaa2a4att5inblhdsb3ww3yysurkahhs3hrd7f535oajsva" \
            --artifact-path "media-server-${version}-compiled.tgz" \
            --artifact-version "${version}-compiled" \
            --content-body "$compiled_package" || {
            warning "Failed to upload compiled package to Artifact Registry"
            warning "You can upload manually later: $compiled_package"
        }
        
        success "🎉 Compiled package ready for instant future deployments!"
    fi
}

# Main function
main() {
    log "🚀 Media Server Deployment Pipeline"
    log "=================================="
    
    # Check prerequisites
    check_media_server
    
    # Get version
    local version=$(get_version)
    log "Deploying version: $version"

    # Package source code
    package_source "$version"
    local package_file="$PROJECT_ROOT/media-server-${version}.tgz"

    # Upload to OCI Object Storage
    upload_package "$package_file" "$version"
    
    # Deploy using Terraform
    deploy_terraform
    
    # Get server IP for monitoring
    cd "$TERRAFORM_DIR"
    local server_ip=$(terraform output -raw media_server_public_ip 2>/dev/null || echo "unknown")
    
    # Monitor deployment (optional)
    if [ "$server_ip" != "unknown" ]; then
        log "Deployment initiated. Server will compile for ~8 minutes."
        log "Monitor progress: ssh -i ~/.ssh/oci_key opc@$server_ip 'sudo tail -f /var/log/startup-script.log'"
        
        # Package compiled result if requested
        if [ "$PACKAGE_AFTER_BUILD" = "true" ]; then
            log "Will package compiled result after build completes..."
            log "Waiting for compilation to finish (this may take 8-10 minutes)..."
            
            # Wait for compilation to complete (check for server.js and mediasoup worker)
            local max_wait=600  # 10 minutes
            local wait_time=0
            while [ $wait_time -lt $max_wait ]; do
                if ssh -i ~/.ssh/oci_key -o StrictHostKeyChecking=no opc@"$server_ip" \
                   'test -f /opt/media-server/server.js && test -f /opt/media-server/node_modules/mediasoup/worker/out/Release/mediasoup-worker' 2>/dev/null; then
                    success "Compilation completed!"
                    package_compiled_result "$server_ip" "$version"
                    break
                fi
                
                sleep 30
                wait_time=$((wait_time + 30))
                log "Still compiling... ($wait_time/${max_wait}s)"
            done
            
            if [ $wait_time -ge $max_wait ]; then
                warning "Compilation taking longer than expected. Check server logs."
            fi
        fi
    fi
    
    success "🎉 Deployment pipeline completed!"
    echo ""
    echo "Next steps:"
    echo "1. Wait for compilation to complete (~8 minutes)"
    echo "2. Test server: https://$server_ip:8080"
    echo "3. For instant future deployments, run: $0 --package"
}

# Run main function
main "$@"
