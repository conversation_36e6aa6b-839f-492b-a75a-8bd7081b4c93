# Network Outputs
output "vcn_id" {
  description = "OCID of the VCN"
  value       = module.networking.vcn_id
}

output "subnet_id" {
  description = "OCID of the subnet"
  value       = module.networking.subnet_id
}

# Media Server Outputs
output "media_server_public_ip" {
  description = "Public IP address of the media server"
  value       = module.media_server.media_server_public_ip
}

output "media_server_private_ip" {
  description = "Private IP address of the media server"
  value       = module.media_server.media_server_private_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.media_server.media_server_url
}

output "media_server_instance_id" {
  description = "OCID of the media server instance"
  value       = module.media_server.instance_id
}

# Signaling Server Outputs
output "signaling_server_public_ip" {
  description = "Public IP address of the signaling server"
  value       = module.signaling_server.public_ip
}

output "signaling_server_private_ip" {
  description = "Private IP address of the signaling server"
  value       = module.signaling_server.private_ip
}

output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = module.signaling_server.signaling_server_url
}

output "signaling_server_instance_id" {
  description = "OCID of the signaling server instance"
  value       = module.signaling_server.instance_id
}

# TURN Server Outputs
output "turn_server_public_ip" {
  description = "Public IP address of the TURN server"
  value       = module.turn_server.public_ip
}

output "turn_server_private_ip" {
  description = "Private IP address of the TURN server"
  value       = module.turn_server.private_ip
}

output "turn_server_instance_id" {
  description = "OCID of the TURN server instance"
  value       = module.turn_server.instance_id
}

# Security Outputs
output "vault_id" {
  description = "OCID of the vault"
  value       = module.security.vault_id
}

output "signaling_dynamic_group_id" {
  description = "OCID of the signaling server dynamic group"
  value       = module.security.signaling_dynamic_group_id
}

output "media_dynamic_group_id" {
  description = "OCID of the media server dynamic group"
  value       = module.security.media_dynamic_group_id
}

output "turn_dynamic_group_id" {
  description = "OCID of the TURN server dynamic group"
  value       = module.security.turn_dynamic_group_id
}

# SSH Commands
output "ssh_commands" {
  description = "SSH commands to connect to instances"
  value = {
    media_server     = "ssh ubuntu@${module.media_server.media_server_public_ip}"
    signaling_server = "ssh ubuntu@${module.signaling_server.public_ip}"
    turn_server      = "ssh ubuntu@${module.turn_server.public_ip}"
  }
}

# Environment Configuration
output "environment_config" {
  description = "Environment configuration for application deployment"
  value = {
    compartment_id       = var.compartment_id
    region               = var.region
    environment          = var.environment
    signaling_server_url = module.signaling_server.signaling_server_url
    media_server_url     = module.media_server.media_server_url
    turn_server_ip       = module.turn_server.public_ip
    turn_username        = var.turn_username
  }
  sensitive = true
}

# Deployment Information
output "deployment_info" {
  description = "Deployment information"
  value = {
    environment = var.environment
    region      = var.region
    media_server = {
      instance_shape = var.media_server_instance_shape
      ocpus          = var.media_server_instance_ocpus
      memory_gb      = var.media_server_instance_memory_gb
      package        = "${var.package_name}-${var.package_version}.tgz"
      architecture   = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.media_server_instance_shape) ? "ARM64" : "x86_64"
    }
    signaling_server = {
      instance_shape = var.signaling_instance_shape
      ocpus          = var.signaling_instance_ocpus
      memory_gb      = var.signaling_instance_memory_gb
    }
    turn_server = {
      instance_shape = var.turn_instance_shape
      ocpus          = var.turn_instance_ocpus
      memory_gb      = var.turn_instance_memory_gb
    }
  }
}
