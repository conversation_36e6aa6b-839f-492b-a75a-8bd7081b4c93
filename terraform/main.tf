# Main Terraform configuration for WebRTC Streaming Platform (OCI)

# Local values for resource naming and tagging
locals {
  name_prefix = "${var.app_name}-${var.environment}"
  common_tags = merge(var.freeform_tags, {
    Environment = var.environment
    Application = var.app_name
    ManagedBy   = "terraform"
  })
}

# Networking Module
module "networking" {
  source = "./modules/networking"

  compartment_id = var.compartment_id
  vcn_name       = "${local.name_prefix}-vcn"
  vcn_cidr       = var.vcn_cidr
  subnet_name    = "${local.name_prefix}-subnet"
  subnet_cidr    = var.subnet_cidr

  freeform_tags = local.common_tags
}

# Security Module (IAM, Dynamic Groups, Vault)
module "security" {
  source = "./modules/security"

  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix
  turn_username  = var.turn_username
  turn_password  = var.turn_password

  freeform_tags = local.common_tags
}

# Media Server Module
module "media_server" {
  source = "./modules/media-server"

  # OCI Configuration
  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix

  # Instance Configuration
  instance_shape     = var.media_server_instance_shape
  instance_ocpus     = var.media_server_instance_ocpus
  instance_memory_gb = var.media_server_instance_memory_gb
  ssh_public_key     = var.ssh_public_key

  # Application Configuration
  package_name    = var.package_name
  package_version = var.package_version
  jwt_secret      = var.jwt_secret

  # Networking
  vcn_id    = module.networking.vcn_id
  subnet_id = module.networking.subnet_id

  freeform_tags = local.common_tags

  depends_on = [module.networking]
}

# Signaling Server Module
module "signaling_server" {
  source = "./modules/signaling-server"

  compartment_id     = var.compartment_id
  name_prefix        = local.name_prefix
  vcn_id             = module.networking.vcn_id
  subnet_id          = module.networking.subnet_id
  instance_shape     = var.signaling_instance_shape
  instance_ocpus     = var.signaling_instance_ocpus
  instance_memory_gb = var.signaling_instance_memory_gb
  ssh_public_key     = var.ssh_public_key
  signaling_image    = var.signaling_image
  media_server_url   = module.media_server.media_server_url
  turn_server_ip     = module.turn_server.public_ip
  turn_username      = var.turn_username
  turn_password      = var.turn_password
  jwt_secret         = var.jwt_secret

  freeform_tags = local.common_tags

  depends_on = [module.networking, module.media_server, module.turn_server]
}

# TURN Server Module
module "turn_server" {
  source = "./modules/turn-server"

  compartment_id     = var.compartment_id
  name_prefix        = local.name_prefix
  vcn_id             = module.networking.vcn_id
  subnet_id          = module.networking.subnet_id
  instance_shape     = var.turn_instance_shape
  instance_ocpus     = var.turn_instance_ocpus
  instance_memory_gb = var.turn_instance_memory_gb
  ssh_public_key     = var.ssh_public_key
  turn_image         = var.turn_image
  turn_username      = var.turn_username
  turn_password      = var.turn_password

  freeform_tags = local.common_tags

  depends_on = [module.networking]
}
