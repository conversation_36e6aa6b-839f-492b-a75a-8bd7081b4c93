# OCI Provider Configuration
variable "compartment_id" {
  description = "OCI Compartment OCID"
  type        = string
}

variable "tenancy_ocid" {
  description = "OCI Tenancy OCID"
  type        = string
}

variable "region" {
  description = "OCI Region"
  type        = string
  default     = "us-ashburn-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# Networking Configuration
variable "vcn_cidr" {
  description = "CIDR block for the VCN"
  type        = string
  default     = "10.0.0.0/16"
}

variable "subnet_cidr" {
  description = "CIDR block for the subnet"
  type        = string
  default     = "********/24"
}

# Application Configuration
variable "app_name" {
  description = "Application name prefix"
  type        = string
  default     = "sai-platform"
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

# Media Server Configuration (small - handles media processing efficiently)
variable "media_server_instance_shape" {
  description = "Instance shape for media server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "media_server_instance_ocpus" {
  description = "Number of OCPUs for media server"
  type        = number
  default     = 1
}

variable "media_server_instance_memory_gb" {
  description = "Memory in GB for media server"
  type        = number
  default     = 6
}

variable "package_name" {
  description = "Name of the media server package"
  type        = string
  default     = "media-server"
}

variable "package_version" {
  description = "Version of the media server package"
  type        = string
  default     = "1.0.0"
}

variable "jwt_secret" {
  description = "JWT secret key for media server authentication"
  type        = string
  sensitive   = true
}

# Signaling Server Configuration (larger - handles many concurrent connections)
variable "signaling_instance_shape" {
  description = "Instance shape for signaling server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "signaling_instance_ocpus" {
  description = "Number of OCPUs for signaling server"
  type        = number
  default     = 2
}

variable "signaling_instance_memory_gb" {
  description = "Memory in GB for signaling server"
  type        = number
  default     = 12
}

variable "signaling_image" {
  description = "Docker image for signaling server"
  type        = string
  default     = "your-registry/signaling-server:latest"
}

# TURN Server Configuration (small - lightweight relay service)
variable "turn_instance_shape" {
  description = "Instance shape for TURN server"
  type        = string
  default     = "VM.Standard.A1.Flex"
}

variable "turn_instance_ocpus" {
  description = "Number of OCPUs for TURN server"
  type        = number
  default     = 1
}

variable "turn_instance_memory_gb" {
  description = "Memory in GB for TURN server"
  type        = number
  default     = 6
}

variable "turn_image" {
  description = "Docker image for TURN server"
  type        = string
  default     = "coturn/coturn:latest"
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  default     = "webrtc"
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  default     = "webrtc123"
  sensitive   = true
}

# Resource Tags
variable "freeform_tags" {
  description = "Freeform tags to apply to all resources"
  type        = map(string)
  default = {
    Project   = "sai-platform"
    ManagedBy = "terraform"
  }
}
