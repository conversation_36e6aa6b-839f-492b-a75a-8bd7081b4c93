variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "tenancy_ocid" {
  description = "OCI Tenancy OCID"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  sensitive   = true
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
