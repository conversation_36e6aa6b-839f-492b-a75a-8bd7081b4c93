# OCI Security Module for WebRTC Platform
# Creates dynamic groups, policies, and vault secrets for OCI resources

# Dynamic Group for Signaling Server Instances
resource "oci_identity_dynamic_group" "signaling_server_dynamic_group" {
  compartment_id = var.tenancy_ocid
  name           = "${var.name_prefix}-signaling-server-dynamic-group"
  description    = "Dynamic group for signaling server instances"

  matching_rule = "instance.compartment.id = '${var.compartment_id}'"

  freeform_tags = var.freeform_tags
}

# Dynamic Group for Media Server Instances
resource "oci_identity_dynamic_group" "media_server_dynamic_group" {
  compartment_id = var.tenancy_ocid
  name           = "${var.name_prefix}-media-server-dynamic-group"
  description    = "Dynamic group for media server instances"

  matching_rule = "instance.compartment.id = '${var.compartment_id}'"

  freeform_tags = var.freeform_tags
}

# Dynamic Group for TURN Server Instances
resource "oci_identity_dynamic_group" "turn_server_dynamic_group" {
  compartment_id = var.tenancy_ocid
  name           = "${var.name_prefix}-turn-server-dynamic-group"
  description    = "Dynamic group for TURN server instances"

  matching_rule = "instance.compartment.id = '${var.compartment_id}'"

  freeform_tags = var.freeform_tags
}

# Policy for Signaling Server
resource "oci_identity_policy" "signaling_server_policy" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-signaling-server-policy"
  description    = "Policy allowing signaling server instances to access required resources"

  statements = [
    "allow dynamic-group ${oci_identity_dynamic_group.signaling_server_dynamic_group.name} to read buckets in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.signaling_server_dynamic_group.name} to read objects in compartment id ${var.compartment_id}"
  ]

  freeform_tags = var.freeform_tags
}

# Policy for Media Server
resource "oci_identity_policy" "media_server_policy" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-media-server-policy"
  description    = "Policy allowing media server instances to access required resources"

  statements = [
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to read buckets in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to read objects in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to manage objects in compartment id ${var.compartment_id}"
  ]

  freeform_tags = var.freeform_tags
}

# Policy for TURN Server (minimal permissions for container instances)
resource "oci_identity_policy" "turn_server_policy" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-turn-server-policy"
  description    = "Policy allowing TURN server instances to access required resources"

  statements = [
    "allow dynamic-group ${oci_identity_dynamic_group.turn_server_dynamic_group.name} to read buckets in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.turn_server_dynamic_group.name} to read objects in compartment id ${var.compartment_id}"
  ]

  freeform_tags = var.freeform_tags
}
