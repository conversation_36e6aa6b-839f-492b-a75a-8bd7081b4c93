output "signaling_server_repo_url" {
  description = "URL of the signaling server container repository"
  value       = "${data.oci_objectstorage_namespace.current.namespace}.ocir.io/${oci_artifacts_container_repository.signaling_server_repo.display_name}"
}

output "turn_server_repo_url" {
  description = "URL of the TURN server container repository"
  value       = "${data.oci_objectstorage_namespace.current.namespace}.ocir.io/${oci_artifacts_container_repository.turn_server_repo.display_name}"
}

output "registry_namespace" {
  description = "OCI Registry namespace"
  value       = data.oci_objectstorage_namespace.current.namespace
}

output "signaling_server_repo_id" {
  description = "ID of the signaling server repository"
  value       = oci_artifacts_container_repository.signaling_server_repo.id
}

output "turn_server_repo_id" {
  description = "ID of the TURN server repository"
  value       = oci_artifacts_container_repository.turn_server_repo.id
}
