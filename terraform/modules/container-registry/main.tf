# OCI Container Registry Module for WebRTC Platform
# Creates container registry and repositories for Docker images

# Create Container Registry
resource "oci_artifacts_container_repository" "signaling_server_repo" {
  compartment_id = var.compartment_id
  display_name   = "${var.name_prefix}/signaling-server"
  is_public      = false

  freeform_tags = var.freeform_tags
}

resource "oci_artifacts_container_repository" "turn_server_repo" {
  compartment_id = var.compartment_id
  display_name   = "${var.name_prefix}/turn-server"
  is_public      = false

  freeform_tags = var.freeform_tags
}

# Get current tenancy namespace for registry URL
data "oci_objectstorage_namespace" "current" {
  compartment_id = var.compartment_id
}
