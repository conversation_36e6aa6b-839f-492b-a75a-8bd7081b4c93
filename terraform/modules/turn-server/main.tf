# OCI TURN Server Module for WebRTC Platform
# Deploys a TURN server on OCI Compute Instance with coturn

# Get the latest Ubuntu image
data "oci_core_images" "ubuntu" {
  compartment_id           = var.compartment_id
  operating_system         = "Canonical Ubuntu"
  operating_system_version = "22.04"
  shape                    = var.instance_shape
  sort_by                  = "TIMECREATED"
  sort_order               = "DESC"
}

# Create TURN server instance
resource "oci_core_instance" "turn_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "${var.name_prefix}-turn-server"
  shape               = var.instance_shape

  shape_config {
    ocpus         = var.instance_ocpus
    memory_in_gbs = var.instance_memory_gb
  }

  create_vnic_details {
    subnet_id                 = var.subnet_id
    display_name              = "${var.name_prefix}-turn-vnic"
    assign_public_ip          = true
    assign_private_dns_record = true
    hostname_label            = "turn"
  }

  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu.images[0].id
  }

  metadata = {
    ssh_authorized_keys = var.ssh_public_key
    user_data = base64encode(templatefile("${path.module}/startup-script.sh", {
      turn_image    = var.turn_image
      turn_username = var.turn_username
      turn_password = var.turn_password
    }))
  }

  freeform_tags = var.freeform_tags
}

# Get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

# Security list for TURN server
resource "oci_core_security_list" "turn_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.name_prefix}-turn-security-list"

  # Allow STUN/TURN traffic (UDP)
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 3478
      max = 3478
    }
  }

  # Allow STUN/TURN traffic (TCP)
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 3478
      max = 3478
    }
  }

  # Allow TLS TURN traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 5349
      max = 5349
    }
  }

  # Allow relay port range (UDP)
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 49152
      max = 49252
    }
  }

  # Allow relay port range (TCP)
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 49152
      max = 49252
    }
  }

  # Allow SSH
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 22
      max = 22
    }
  }

  # Allow all outbound traffic
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }

  freeform_tags = var.freeform_tags
}
