# Outputs for OCI TURN Server Module

output "instance_id" {
  description = "The OCID of the TURN server instance"
  value       = oci_core_instance.turn_server.id
}

output "instance_name" {
  description = "The display name of the TURN server instance"
  value       = oci_core_instance.turn_server.display_name
}

output "public_ip" {
  description = "The public IP address of the TURN server"
  value       = oci_core_instance.turn_server.public_ip
}

output "private_ip" {
  description = "The private IP address of the TURN server"
  value       = oci_core_instance.turn_server.private_ip
}

output "turn_server_ip" {
  description = "The IP address of the TURN server (alias for public_ip)"
  value       = oci_core_instance.turn_server.public_ip
}

output "security_list_id" {
  description = "The OCID of the TURN server security list"
  value       = oci_core_security_list.turn_security_list.id
}
