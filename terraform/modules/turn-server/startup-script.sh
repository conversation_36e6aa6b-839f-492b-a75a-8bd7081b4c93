#!/bin/bash

# TURN Server Startup Script for OCI Ubuntu Instance
# This script installs Dock<PERSON> and runs the coturn TURN server

set -e

# Logging function
log_with_timestamp() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_with_timestamp "Starting TURN server setup..."

# Update system
log_with_timestamp "Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install Docker
log_with_timestamp "Installing Docker..."
apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
apt-get update -y
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Start and enable Docker
systemctl start docker
systemctl enable docker

# Add ubuntu user to docker group
usermod -aG docker ubuntu

log_with_timestamp "Docker installation completed"

# Create TURN server directory
mkdir -p /opt/turn-server
cd /opt/turn-server

# Create coturn configuration directory
mkdir -p /etc/coturn

# Create coturn configuration file
cat > /etc/coturn/turnserver.conf << EOF
# coturn TURN server configuration for WebRTC

# Listening port for TURN/STUN
listening-port=3478

# TLS listening port (optional, for secure connections)
tls-listening-port=5349

# Relay ports range (for media)
min-port=49152
max-port=49252

# Enable verbose logging
verbose

# Log file (will go to stdout in Docker)
log-file=stdout

# Realm for authentication
realm=webrtc.local

# Server name
server-name=turn-server

# Authentication
# Use long-term credentials
lt-cred-mech

# Static user credentials
user=${turn_username}:${turn_password}

# Allow loopback peers (for local testing)
allow-loopback-peers

# Disable RFC5780 support (can cause issues)
no-rfc5780

# Enable STUN
stun-only=false

# Disable software attributes
no-software-attribute
EOF

# Create docker-compose file
cat > docker-compose.yml << EOF
version: '3.8'
services:
  coturn:
    image: ${turn_image}
    container_name: coturn-server
    restart: unless-stopped
    network_mode: host
    volumes:
      - /etc/coturn/turnserver.conf:/etc/coturn/turnserver.conf:ro
    environment:
      - TURN_USERNAME=${turn_username}
      - TURN_PASSWORD=${turn_password}
    command: turnserver -c /etc/coturn/turnserver.conf -v
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
EOF

# Create systemd service for TURN server
cat > /etc/systemd/system/turn-server.service << EOF
[Unit]
Description=TURN Server (coturn)
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/turn-server
ExecStart=/usr/bin/docker compose up -d
ExecStop=/usr/bin/docker compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable turn-server.service

log_with_timestamp "Pulling TURN server image..."
docker pull ${turn_image}

log_with_timestamp "Starting TURN server..."
systemctl start turn-server.service

# Wait for service to be ready
log_with_timestamp "Waiting for TURN server to be ready..."
sleep 30

# Check if service is running
if systemctl is-active --quiet turn-server.service; then
    log_with_timestamp "✅ TURN server is running successfully"
    docker ps | grep coturn-server
else
    log_with_timestamp "❌ TURN server failed to start"
    systemctl status turn-server.service
    docker logs coturn-server
    exit 1
fi

log_with_timestamp "TURN server setup completed successfully"
