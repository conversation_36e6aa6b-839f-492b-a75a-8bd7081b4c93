# OCI Object Storage Module for Client App
# Creates a public object storage bucket for hosting the React client application

# Get current namespace
data "oci_objectstorage_namespace" "current" {
  compartment_id = var.compartment_id
}

# Create Object Storage bucket for client app
resource "oci_objectstorage_bucket" "client_app_bucket" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-client-app"
  namespace      = data.oci_objectstorage_namespace.current.namespace

  # Make bucket publicly accessible
  access_type = "ObjectRead"

  freeform_tags = var.freeform_tags
}

# Create a PAR (Pre-Authenticated Request) for public access to the bucket
resource "oci_objectstorage_preauthrequest" "client_app_par" {
  namespace    = data.oci_objectstorage_namespace.current.namespace
  bucket       = oci_objectstorage_bucket.client_app_bucket.name
  name         = "${var.name_prefix}-client-app-par"
  access_type  = "ObjectRead"
  time_expires = timeadd(timestamp(), "8760h") # 1 year from now

  # Allow access to all objects in the bucket
  object_name = "*"
}

# Create index.html object for SPA routing
resource "oci_objectstorage_object" "index_html" {
  namespace = data.oci_objectstorage_namespace.current.namespace
  bucket    = oci_objectstorage_bucket.client_app_bucket.name
  object    = "index.html"
  
  # Placeholder content - will be replaced by actual build
  content = templatefile("${path.module}/templates/index.html", {
    signaling_server_url = var.signaling_server_url
  })
  
  content_type = "text/html"
  
  metadata = {
    "cache-control" = "no-cache"
  }
}
