output "bucket_name" {
  description = "Name of the client app storage bucket"
  value       = oci_objectstorage_bucket.client_app_bucket.name
}

output "bucket_namespace" {
  description = "Namespace of the client app storage bucket"
  value       = data.oci_objectstorage_namespace.current.namespace
}

output "public_url" {
  description = "Public URL for accessing the client app"
  value       = "https://objectstorage.${var.region}.oraclecloud.com/n/${data.oci_objectstorage_namespace.current.namespace}/b/${oci_objectstorage_bucket.client_app_bucket.name}/o/index.html"
}

output "bucket_url" {
  description = "Base URL for the bucket"
  value       = "https://objectstorage.${var.region}.oraclecloud.com/n/${data.oci_objectstorage_namespace.current.namespace}/b/${oci_objectstorage_bucket.client_app_bucket.name}/o"
}

output "par_url" {
  description = "Pre-authenticated request URL for public access"
  value       = oci_objectstorage_preauthrequest.client_app_par.full_path
}
