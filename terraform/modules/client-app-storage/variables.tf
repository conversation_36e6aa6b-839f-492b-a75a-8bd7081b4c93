variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "signaling_server_url" {
  description = "URL of the signaling server for client configuration"
  type        = string
}

variable "region" {
  description = "OCI region"
  type        = string
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
