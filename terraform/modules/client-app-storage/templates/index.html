<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Switcher.AI - Professional WebRTC Streaming</title>
    <script>
      // Configure signaling server URL for production
      window.SIGNALING_SERVER_URL = '${signaling_server_url}';
    </script>
  </head>
  <body>
    <div id="root">
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #1f2937, #111827); color: white; font-family: system-ui, -apple-system, sans-serif;">
        <div style="text-align: center;">
          <h1 style="font-size: 2rem; margin-bottom: 1rem;">Switcher.AI</h1>
          <p style="margin-bottom: 2rem; opacity: 0.8;">Loading application...</p>
          <div style="width: 48px; height: 48px; border: 4px solid #f97316; border-top: 4px solid transparent; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
        </div>
      </div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
