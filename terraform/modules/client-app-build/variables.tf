variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "bucket_name" {
  description = "Name of the object storage bucket"
  type        = string
}

variable "bucket_namespace" {
  description = "Namespace of the object storage bucket"
  type        = string
}

variable "signaling_server_url" {
  description = "URL of the signaling server"
  type        = string
}

variable "region" {
  description = "OCI region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "force_rebuild" {
  description = "Force rebuild of the client app"
  type        = string
  default     = ""
}
