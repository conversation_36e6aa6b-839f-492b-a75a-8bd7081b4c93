#!/bin/bash

# Build and Deploy Script for SAI Platform Client App
# This script builds the React app and deploys it to OCI Object Storage

set -e

# Configuration
BUCKET_NAME="${bucket_name}"
BUCKET_NAMESPACE="${bucket_namespace}"
SIGNALING_SERVER_URL="${signaling_server_url}"
REGION="${region}"
COMPARTMENT_ID="${compartment_id}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "$${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]$${NC} $1"
}

success() {
    echo -e "$${GREEN}✅ $1$${NC}"
}

warning() {
    echo -e "$${YELLOW}⚠️  $1$${NC}"
}

error() {
    echo -e "$${RED}❌ $1$${NC}"
    exit 1
}

# Check prerequisites
log "Checking prerequisites..."

if ! command -v node &> /dev/null; then
    error "Node.js is not installed. Please install Node.js 18+ to continue."
fi

if ! command -v npm &> /dev/null; then
    error "npm is not installed. Please install npm to continue."
fi

if ! command -v oci &> /dev/null; then
    error "OCI CLI is not installed. Please install and configure OCI CLI to continue."
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    error "package.json not found. Please run this script from the project root directory."
fi

if [ ! -d "src" ]; then
    error "src directory not found. Please run this script from the project root directory."
fi

# Create production config
log "Creating production configuration..."
mkdir -p src/config
cat > src/config/production.json << EOF
{
  "signalingServerUrl": "$SIGNALING_SERVER_URL",
  "environment": "production",
  "buildTime": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

success "Production configuration created"

# Install dependencies
log "Installing dependencies..."
npm ci

# Build the application
log "Building React application..."
export NODE_ENV=production
export VITE_SIGNALING_SERVER_URL="$SIGNALING_SERVER_URL"
npm run build

if [ ! -d "dist" ]; then
    error "Build failed - dist directory not found"
fi

success "React application built successfully"

# Upload to OCI Object Storage
log "Uploading to OCI Object Storage..."

# Upload all files from dist directory
find dist -type f | while read file; do
    # Get relative path
    relative_path=$${file#dist/}
    
    # Determine content type
    case "$file" in
        *.html) content_type="text/html" ;;
        *.css) content_type="text/css" ;;
        *.js) content_type="application/javascript" ;;
        *.json) content_type="application/json" ;;
        *.png) content_type="image/png" ;;
        *.jpg) content_type="image/jpeg" ;;
        *.jpeg) content_type="image/jpeg" ;;
        *.svg) content_type="image/svg+xml" ;;
        *.ico) content_type="image/x-icon" ;;
        *) content_type="application/octet-stream" ;;
    esac
    
    log "Uploading $$relative_path..."
    
    oci os object put \
        --namespace "$$BUCKET_NAMESPACE" \
        --bucket-name "$$BUCKET_NAME" \
        --name "$$relative_path" \
        --file "$$file" \
        --content-type "$$content_type" \
        --force
done

success "All files uploaded to OCI Object Storage"

# Create a simple deployment summary
log "Deployment Summary:"
echo "  Bucket: $$BUCKET_NAME"
echo "  Namespace: $$BUCKET_NAMESPACE"
echo "  Region: $$REGION"
echo "  Signaling Server: $$SIGNALING_SERVER_URL"
echo "  Public URL: https://objectstorage.$$REGION.oraclecloud.com/n/$$BUCKET_NAMESPACE/b/$$BUCKET_NAME/o/index.html"

success "Client app deployment completed successfully!"
