# Outputs for OCI Networking Module

output "vcn_id" {
  description = "The OCID of the VCN"
  value       = oci_core_vcn.vcn.id
}

output "vcn_name" {
  description = "The name of the VCN"
  value       = oci_core_vcn.vcn.display_name
}

output "subnet_id" {
  description = "The OCID of the public subnet"
  value       = oci_core_subnet.public_subnet.id
}

output "subnet_name" {
  description = "The name of the public subnet"
  value       = oci_core_subnet.public_subnet.display_name
}

output "internet_gateway_id" {
  description = "The OCID of the internet gateway"
  value       = oci_core_internet_gateway.internet_gateway.id
}

output "nat_gateway_id" {
  description = "The OCID of the NAT gateway"
  value       = oci_core_nat_gateway.nat_gateway.id
}

output "service_gateway_id" {
  description = "The OCID of the service gateway"
  value       = oci_core_service_gateway.service_gateway.id
}

output "public_security_list_id" {
  description = "The OCID of the public security list"
  value       = oci_core_security_list.public_security_list.id
}
