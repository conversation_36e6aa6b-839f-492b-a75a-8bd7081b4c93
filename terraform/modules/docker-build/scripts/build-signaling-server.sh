#!/bin/bash

# Build and Push Signaling Server Docker Image
# This script builds the signaling server image and pushes it to OCI Container Registry

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() { echo -e "$${BLUE}[$(date +'%H:%M:%S')]$${NC} $1"; }
success() { echo -e "$${GREEN}✅ $1$${NC}"; }
warning() { echo -e "$${YELLOW}⚠️  $1$${NC}"; }
error() { echo -e "$${RED}❌ $1$${NC}"; exit 1; }

# Configuration from Terraform
REGISTRY_URL="${registry_url}"
IMAGE_TAG="${image_tag}"
REGION="${region}"
PROJECT_ROOT="${project_root}"

# Derived values
FULL_IMAGE_URL="$${REGISTRY_URL}:$${IMAGE_TAG}"

log "Building signaling server Docker image..."
log "Registry URL: $${REGISTRY_URL}"
log "Image Tag: $${IMAGE_TAG}"
log "Full Image URL: $${FULL_IMAGE_URL}"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    error "Docker is not installed. Please install Docker first."
fi

# Check if OCI CLI is installed
if ! command -v oci &> /dev/null; then
    error "OCI CLI is not installed. Please install OCI CLI first."
fi

# Navigate to project root
cd "$${PROJECT_ROOT}"

# Verify Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    error "Dockerfile not found in project root: $${PROJECT_ROOT}"
fi

# Configure Docker authentication for OCI Container Registry
log "Configuring Docker authentication for OCI Container Registry..."

# Get the registry endpoint for authentication
REGISTRY_ENDPOINT="${region}.ocir.io"
log "Authenticating with registry: $${REGISTRY_ENDPOINT}"

# Use existing OCI session for Docker login
log "Attempting Docker login with existing OCI credentials (profile: mike)..."
if ! docker login "$${REGISTRY_ENDPOINT}" 2>/dev/null; then
    log "Direct login failed, trying with OCI auth helper..."
    # Try using OCI CLI to get auth token for Docker
    export OCI_CLI_PROFILE=mike
    if ! oci session validate --profile mike 2>/dev/null; then
        error "OCI session expired or invalid. Please run 'oci session authenticate --region ${region} --profile-name mike' first, then re-run terraform apply."
    fi

    # Try Docker login again with profile set
    if ! docker login "$${REGISTRY_ENDPOINT}" 2>/dev/null; then
        error "Failed to authenticate with OCI Container Registry. Please ensure your OCI session is valid."
    fi
fi

# Build the Docker image
log "Building signaling server image..."
docker build \
    --platform linux/amd64 \
    -t "$${FULL_IMAGE_URL}" \
    -f Dockerfile \
    . || error "Failed to build signaling server image"

success "Signaling server image built successfully"

# Push the image to OCI Container Registry
log "Pushing image to OCI Container Registry..."
docker push "$${FULL_IMAGE_URL}" || error "Failed to push signaling server image"

success "Signaling server image pushed successfully: $${FULL_IMAGE_URL}"

# Clean up local image to save space (optional)
log "Cleaning up local image..."
docker rmi "$${FULL_IMAGE_URL}" || warning "Failed to remove local image (this is okay)"

success "Signaling server build and push completed!"
