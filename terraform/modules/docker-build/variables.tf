# Docker Build Module Variables

variable "compartment_id" {
  description = "OCI compartment ID"
  type        = string
}

variable "region" {
  description = "OCI region"
  type        = string
}

variable "project_root" {
  description = "Path to the project root directory"
  type        = string
}

variable "signaling_server_repo_url" {
  description = "OCI Container Registry URL for signaling server"
  type        = string
}

variable "turn_server_repo_url" {
  description = "OCI Container Registry URL for TURN server"
  type        = string
}

variable "signaling_image_tag" {
  description = "Tag for the signaling server image"
  type        = string
  default     = "latest"
}

variable "turn_image_tag" {
  description = "Tag for the TURN server image"
  type        = string
  default     = "latest"
}

variable "force_rebuild" {
  description = "Force rebuild of images even if no changes detected"
  type        = bool
  default     = false
}
