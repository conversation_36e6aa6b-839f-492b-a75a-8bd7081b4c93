# Docker Build Module Outputs

output "signaling_server_image_url" {
  description = "Full URL of the built signaling server image"
  value       = "${var.signaling_server_repo_url}:${var.signaling_image_tag}"
}

output "turn_server_image_url" {
  description = "Full URL of the built TURN server image"
  value       = "${var.turn_server_repo_url}:${var.turn_image_tag}"
}

output "build_scripts" {
  description = "Paths to generated build scripts"
  value = {
    signaling_server = "${path.root}/build-signaling-server.sh"
    turn_server      = "${path.root}/build-turn-server.sh"
    all_images       = "${path.root}/build-all-images.sh"
  }
}

output "build_status" {
  description = "Build status information"
  value = {
    signaling_server_built = null_resource.build_signaling_server.id != null
    turn_server_built      = null_resource.build_turn_server.id != null
    last_build_time        = timestamp()
  }
}
