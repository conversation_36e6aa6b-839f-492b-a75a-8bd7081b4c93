# Docker Build and Push Module
# Builds and pushes Docker images to OCI Container Registry

# Get current namespace for registry URLs
data "oci_objectstorage_namespace" "current" {
  compartment_id = var.compartment_id
}

# Create build scripts for each image
resource "local_file" "signaling_server_build_script" {
  content = templatefile("${path.module}/scripts/build-signaling-server.sh", {
    registry_url     = var.signaling_server_repo_url
    image_tag        = var.signaling_image_tag
    region           = var.region
    project_root     = var.project_root
  })
  filename        = "${path.root}/build-signaling-server.sh"
  file_permission = "0755"
}

resource "local_file" "turn_server_build_script" {
  content = templatefile("${path.module}/scripts/build-turn-server.sh", {
    registry_url     = var.turn_server_repo_url
    image_tag        = var.turn_image_tag
    region           = var.region
    project_root     = var.project_root
  })
  filename        = "${path.root}/build-turn-server.sh"
  file_permission = "0755"
}

# Build and push signaling server image
resource "null_resource" "build_signaling_server" {
  # Trigger rebuild when source code changes or when forced
  triggers = {
    dockerfile_hash    = filemd5("${var.project_root}/Dockerfile")
    package_json_hash  = filemd5("${var.project_root}/package.json")
    server_code_hash   = sha256(join("", [for f in fileset("${var.project_root}/server/src", "**/*.ts") : filemd5("${var.project_root}/server/src/${f}")]))
    force_rebuild      = var.force_rebuild
    script_hash        = local_file.signaling_server_build_script.content_md5
    image_tag          = var.signaling_image_tag
  }

  provisioner "local-exec" {
    command     = "./build-signaling-server.sh"
    working_dir = path.root
    environment = {
      OCI_CLI_REGION = var.region
    }
  }

  depends_on = [local_file.signaling_server_build_script]
}

# Build and push TURN server image
resource "null_resource" "build_turn_server" {
  # Trigger rebuild when source code changes or when forced
  triggers = {
    dockerfile_hash   = filemd5("${var.project_root}/turn-server/Dockerfile")
    config_hash       = filemd5("${var.project_root}/turn-server/turnserver.conf")
    force_rebuild     = var.force_rebuild
    script_hash       = local_file.turn_server_build_script.content_md5
    image_tag         = var.turn_image_tag
  }

  provisioner "local-exec" {
    command     = "./build-turn-server.sh"
    working_dir = path.root
    environment = {
      OCI_CLI_REGION = var.region
    }
  }

  depends_on = [local_file.turn_server_build_script]
}

# Create a combined build script for manual use
resource "local_file" "build_all_script" {
  content = templatefile("${path.module}/scripts/build-all.sh", {
    signaling_registry_url = var.signaling_server_repo_url
    turn_registry_url      = var.turn_server_repo_url
    signaling_image_tag    = var.signaling_image_tag
    turn_image_tag         = var.turn_image_tag
    region                 = var.region
    project_root           = var.project_root
  })
  filename        = "${path.root}/build-all-images.sh"
  file_permission = "0755"
}
