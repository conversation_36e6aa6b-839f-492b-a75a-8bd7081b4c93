# OCI Signaling Server Container Instance Module for WebRTC Platform
# Deploys a signaling server using OCI Container Instance

# Get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

# Create Container Instance for signaling server
resource "oci_container_instances_container_instance" "signaling_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "${var.name_prefix}-signaling"
  shape               = var.instance_shape

  shape_config {
    ocpus         = var.instance_ocpus
    memory_in_gbs = var.instance_memory_gb
  }

  vnics {
    subnet_id      = var.subnet_id
    display_name   = "${var.name_prefix}-signaling-vnic"
    hostname_label = "signaling"
    is_public_ip_assigned = true
  }

  containers {
    display_name = "signaling-server"
    image_url    = var.signaling_image

    environment_variables = {
      NODE_ENV          = "production"
      PORT              = "3001"
      MEDIA_SERVER_URL  = var.media_server_url
      TURN_SERVER_IP    = var.turn_server_ip
      TURN_USERNAME     = var.turn_username
      TURN_PASSWORD     = var.turn_password
      JWT_SECRET        = var.jwt_secret
      MONGODB_URI       = var.mongodb_uri
    }

    resource_config {
      memory_limit_in_gbs = var.container_memory_gb
      vcpus_limit         = var.container_vcpus
    }

    health_checks {
      health_check_type = "HTTP"
      path              = "/health"
      port              = 3001
      interval_in_seconds = 30
      timeout_in_seconds  = 10
      failure_threshold   = 3
      success_threshold   = 1
    }
  }

  freeform_tags = var.freeform_tags
}

# Security list for signaling server
resource "oci_core_security_list" "signaling_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.name_prefix}-signaling-security-list"

  # Allow inbound HTTP traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 80
      max = 80
    }
  }

  # Allow inbound HTTPS traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 443
      max = 443
    }
  }

  # Allow inbound signaling server traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 3001
      max = 3001
    }
  }

  # Allow all outbound traffic
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }

  freeform_tags = var.freeform_tags
}
