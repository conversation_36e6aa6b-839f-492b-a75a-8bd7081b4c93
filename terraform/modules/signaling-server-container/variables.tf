variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vcn_id" {
  description = "VCN ID"
  type        = string
}

variable "subnet_id" {
  description = "Subnet ID"
  type        = string
}

variable "instance_shape" {
  description = "Container instance shape"
  type        = string
  default     = "CI.Standard.E4.Flex"
}

variable "instance_ocpus" {
  description = "Number of OCPUs for the container instance"
  type        = number
  default     = 1
}

variable "instance_memory_gb" {
  description = "Memory in GB for the container instance"
  type        = number
  default     = 8
}

variable "container_vcpus" {
  description = "Number of vCPUs for the container"
  type        = number
  default     = 1
}

variable "container_memory_gb" {
  description = "Memory in GB for the container"
  type        = number
  default     = 4
}

variable "signaling_image" {
  description = "Docker image for signaling server"
  type        = string
}

variable "media_server_url" {
  description = "Media server URL"
  type        = string
}

variable "turn_server_ip" {
  description = "TURN server IP address"
  type        = string
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT secret key"
  type        = string
  sensitive   = true
}

variable "mongodb_uri" {
  description = "MongoDB connection URI"
  type        = string
  sensitive   = true
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
