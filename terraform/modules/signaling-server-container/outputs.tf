output "container_instance_id" {
  description = "ID of the signaling server container instance"
  value       = oci_container_instances_container_instance.signaling_server.id
}

# Note: Container instances may not expose IP addresses directly
# We'll use the load balancer URL instead for accessing the signaling server
output "public_ip" {
  description = "Public IP address of the signaling server (check OCI console)"
  value       = "Check OCI Console for Container Instance: ${oci_container_instances_container_instance.signaling_server.display_name}"
}

output "private_ip" {
  description = "Private IP address of the signaling server (check OCI console)"
  value       = "Check OCI Console for Container Instance: ${oci_container_instances_container_instance.signaling_server.display_name}"
}

output "signaling_server_url" {
  description = "URL of the signaling server (use load balancer URL instead)"
  value       = "Use load balancer URL from dns module outputs"
}

output "hostname" {
  description = "Hostname of the signaling server"
  value       = "${oci_container_instances_container_instance.signaling_server.display_name}.${oci_container_instances_container_instance.signaling_server.availability_domain}"
}
