#!/bin/bash

# SSL Certificate Setup Script
# This script runs after the instance is fully deployed and configured

set -e

# Logging function
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Get the public IP using multiple methods
PUBLIC_IP=""

# Try OCI metadata endpoint
PUBLIC_IP=$(curl -s -H "Authorization: Bearer Oracle" http://***************/opc/v2/instance/networkInterfaces/0/publicIp 2>/dev/null || echo "")

# If that fails, try the v1 endpoint
if [ -z "$PUBLIC_IP" ] || [[ "$PUBLIC_IP" == *"<html>"* ]]; then
    PUBLIC_IP=$(curl -s http://***************/opc/v1/instance/networkInterfaces/0/publicIp 2>/dev/null || echo "")
fi

# If that fails, try external service
if [ -z "$PUBLIC_IP" ] || [[ "$PUBLIC_IP" == *"<html>"* ]]; then
    PUBLIC_IP=$(curl -s https://ipinfo.io/ip 2>/dev/null || echo "")
fi

# If that fails, try another external service
if [ -z "$PUBLIC_IP" ] || [[ "$PUBLIC_IP" == *"<html>"* ]]; then
    PUBLIC_IP=$(curl -s https://api.ipify.org 2>/dev/null || echo "")
fi

if [ -z "$PUBLIC_IP" ] || [[ "$PUBLIC_IP" == *"<html>"* ]]; then
    log_with_timestamp "❌ Could not detect public IP"
    exit 1
fi

log_with_timestamp "🌐 Detected public IP: $PUBLIC_IP"

# Use nip.io domain for Let's Encrypt (resolves to our IP automatically)
DOMAIN="media-server.$PUBLIC_IP.nip.io"
log_with_timestamp "🔒 Setting up Let's Encrypt certificate for domain: $DOMAIN"

# Test if domain resolves correctly
log_with_timestamp "🔍 Testing domain resolution..."
RESOLVED_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "")
if [ "$RESOLVED_IP" != "$PUBLIC_IP" ]; then
    log_with_timestamp "⚠️ Domain $DOMAIN does not resolve to $PUBLIC_IP (got: $RESOLVED_IP)"
    log_with_timestamp "⚠️ Falling back to self-signed certificate"
    exit 1
fi

log_with_timestamp "✅ Domain resolves correctly"

# Stop media server temporarily to free up ports
log_with_timestamp "⏸️ Stopping media server temporarily..."
systemctl stop media-server || true

# Install certbot if not already installed
if ! command -v certbot &> /dev/null; then
    log_with_timestamp "📦 Installing certbot..."
    apt-get update
    apt-get install -y certbot
fi

# Generate Let's Encrypt certificate using standalone mode
log_with_timestamp "🔐 Requesting Let's Encrypt certificate..."
certbot certonly --standalone --non-interactive --agree-tos \
    --email "admin@$DOMAIN" \
    --domains "$DOMAIN" \
    --http-01-port 80 \
    --preferred-challenges http || {
    
    log_with_timestamp "❌ Let's Encrypt failed, keeping existing certificate"
    systemctl start media-server
    exit 1
}

# Copy certificates to media server location
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    log_with_timestamp "✅ Let's Encrypt certificate obtained successfully"
    
    # Backup existing certificates
    if [ -f "/opt/media-server/ssl/cert.pem" ]; then
        cp /opt/media-server/ssl/cert.pem /opt/media-server/ssl/cert.pem.backup
        cp /opt/media-server/ssl/key.pem /opt/media-server/ssl/key.pem.backup
    fi
    
    # Install new certificates
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" /opt/media-server/ssl/cert.pem
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" /opt/media-server/ssl/key.pem
    
    # Set proper permissions
    chmod 644 /opt/media-server/ssl/cert.pem
    chmod 600 /opt/media-server/ssl/key.pem
    
    # Store domain for later use
    echo "$DOMAIN" > /opt/media-server/ssl/domain.txt
    
    log_with_timestamp "🎉 SSL certificate successfully installed"
else
    log_with_timestamp "❌ Certificate files not found after certbot"
    systemctl start media-server
    exit 1
fi

# Set up automatic renewal
log_with_timestamp "⏰ Setting up automatic certificate renewal..."
cat > /etc/cron.d/certbot-renew << EOF
# Renew Let's Encrypt certificates twice daily
0 */12 * * * root certbot renew --quiet --post-hook "systemctl reload media-server"
EOF

# Restart media server with new certificate
log_with_timestamp "🚀 Restarting media server with new SSL certificate..."
systemctl start media-server

# Wait a moment and test
sleep 5
if systemctl is-active --quiet media-server; then
    log_with_timestamp "✅ Media server restarted successfully with SSL certificate"
    log_with_timestamp "🌐 Server available at: https://$DOMAIN:8080"
else
    log_with_timestamp "❌ Media server failed to start with new certificate"
    log_with_timestamp "🔄 Restoring backup certificates..."
    
    if [ -f "/opt/media-server/ssl/cert.pem.backup" ]; then
        cp /opt/media-server/ssl/cert.pem.backup /opt/media-server/ssl/cert.pem
        cp /opt/media-server/ssl/key.pem.backup /opt/media-server/ssl/key.pem
        systemctl start media-server
        log_with_timestamp "✅ Restored backup certificates and restarted server"
    fi
    exit 1
fi

log_with_timestamp "🎉 SSL setup completed successfully!"
