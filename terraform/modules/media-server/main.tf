# OCI Media Server VM for WebRTC-to-RTMP conversion
# Uses Ampere A1 instance for cost-effective ARM64 deployment



# Get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

# Generate random suffix for unique hostname
# This will regenerate whenever the package version changes
resource "random_id" "hostname_suffix" {
  byte_length = 4
  keepers = {
    package_version = var.package_version
  }
}

# Dynamic Group for Media Server Instance
resource "oci_identity_dynamic_group" "media_server_dynamic_group" {
  compartment_id = var.tenancy_ocid
  name           = "${var.name_prefix}-media-server-dynamic-group"
  description    = "Dynamic group for media server instances to access Object Storage"

  matching_rule = "instance.compartment.id = '${var.compartment_id}'"

  freeform_tags = var.freeform_tags
}

# Policy to allow media server instances to access Object Storage
resource "oci_identity_policy" "media_server_policy" {
  compartment_id = var.compartment_id
  name           = "${var.name_prefix}-media-server-policy"
  description    = "Policy allowing media server instances to access Object Storage"

  statements = [
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to read buckets in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to read objects in compartment id ${var.compartment_id}",
    "allow dynamic-group ${oci_identity_dynamic_group.media_server_dynamic_group.name} to manage objects in compartment id ${var.compartment_id} where target.bucket.name='${var.name_prefix}-media-server-repo'"
  ]

  freeform_tags = var.freeform_tags
}

# Get the latest Ubuntu 22.04 LTS image (ARM64 for A1/A2, x86 for others)
data "oci_core_images" "ubuntu" {
  compartment_id           = var.compartment_id
  operating_system         = "Canonical Ubuntu"
  operating_system_version = "22.04"
  shape                    = var.instance_shape
  sort_by                  = "TIMECREATED"
  sort_order               = "DESC"
}

# Note: VCN and subnet are provided by the networking module

# Security List for Media Server
resource "oci_core_security_list" "media_server_seclist" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.name_prefix}-media-server-seclist"

  # Egress rules - allow all outbound
  egress_security_rules {
    destination = "0.0.0.0/0"
    protocol    = "all"
  }

  # SSH access
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 22
      max = 22
    }
  }

  # HTTP for Let's Encrypt challenge
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 80
      max = 80
    }
  }

  # HTTPS/HTTP for media server
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 8080
      max = 8081
    }
  }

  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 443
      max = 443
    }
  }

  # WebRTC media ports (UDP) - Mediasoup worker port range
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 40000
      max = 40100
    }
  }

  # WebRTC media ports (TCP fallback) - Mediasoup worker port range
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 40000
      max = 40100
    }
  }

  # RTP ports for FFmpeg (CRITICAL for streaming)
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 5004
      max = 5007
    }
  }

  freeform_tags = var.freeform_tags
}



# Media Server Instance (Ampere A1)
resource "oci_core_instance" "media_server" {
  compartment_id      = var.compartment_id
  availability_domain = var.availability_domain != null ? var.availability_domain : data.oci_identity_availability_domains.ads.availability_domains[0].name
  display_name        = "${var.name_prefix}-media-server"
  shape               = var.instance_shape

  dynamic "shape_config" {
    for_each = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.instance_shape) ? [1] : []
    content {
      ocpus         = var.instance_ocpus
      memory_in_gbs = var.instance_memory_gb
    }
  }

  create_vnic_details {
    subnet_id        = var.subnet_id
    display_name     = "${var.name_prefix}-media-server-vnic"
    assign_public_ip = true
    hostname_label   = "media-server-${random_id.hostname_suffix.hex}"
  }

  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu.images[0].id
  }

  metadata = {
    ssh_authorized_keys = var.ssh_public_key
    user_data = base64encode(join("\n", [
      templatefile("${path.module}/startup-script-ubuntu.sh", {
        announced_ip       = "AUTO_DETECT" # Will be detected at runtime
        package_name       = var.package_name
        package_version    = var.package_version
        mediasoup_min_port = var.mediasoup_min_port
        mediasoup_max_port = var.mediasoup_max_port
        jwt_secret         = var.jwt_secret
      }),
      "",
      "# SSL Setup Script",
      "cat > /opt/media-server/setup-ssl.sh << 'EOF'",
      file("${path.module}/setup-ssl.sh"),
      "EOF",
      "chmod +x /opt/media-server/setup-ssl.sh"
    ]))
  }

  freeform_tags = var.freeform_tags

  lifecycle {
    create_before_destroy = true
  }
}


