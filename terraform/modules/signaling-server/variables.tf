# Variables for OCI Signaling Server Module

variable "compartment_id" {
  description = "The OCID of the compartment where resources will be created"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vcn_id" {
  description = "The OCID of the VCN"
  type        = string
}

variable "subnet_id" {
  description = "The OCID of the subnet where the instance will be created"
  type        = string
}

variable "instance_shape" {
  description = "The shape of the compute instance"
  type        = string
  default     = "VM.Standard.A1.Flex"
}

variable "instance_ocpus" {
  description = "The number of OCPUs for the instance"
  type        = number
  default     = 1
}

variable "instance_memory_gb" {
  description = "The amount of memory in GB for the instance"
  type        = number
  default     = 6
}

variable "ssh_public_key" {
  description = "The SSH public key for instance access"
  type        = string
}

variable "signaling_image" {
  description = "Docker image for the signaling server"
  type        = string
}

variable "media_server_url" {
  description = "URL of the media server"
  type        = string
}

variable "turn_server_ip" {
  description = "IP address of the TURN server"
  type        = string
}

variable "turn_username" {
  description = "Username for TURN server authentication"
  type        = string
}

variable "turn_password" {
  description = "Password for TURN server authentication"
  type        = string
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT secret for authentication"
  type        = string
  sensitive   = true
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
