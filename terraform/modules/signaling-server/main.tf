# OCI Signaling Server Module for WebRTC Platform
# Deploys a signaling server on OCI Compute Instance with Dock<PERSON>

# Get the latest Ubuntu image
data "oci_core_images" "ubuntu" {
  compartment_id           = var.compartment_id
  operating_system         = "Canonical Ubuntu"
  operating_system_version = "22.04"
  shape                    = var.instance_shape
  sort_by                  = "TIMECREATED"
  sort_order               = "DESC"
}

# Create signaling server instance
resource "oci_core_instance" "signaling_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "${var.name_prefix}-signaling"
  shape               = var.instance_shape

  shape_config {
    ocpus         = var.instance_ocpus
    memory_in_gbs = var.instance_memory_gb
  }

  create_vnic_details {
    subnet_id                 = var.subnet_id
    display_name              = "${var.name_prefix}-signaling-vnic"
    assign_public_ip          = true
    assign_private_dns_record = true
    hostname_label            = "signaling"
  }

  source_details {
    source_type = "image"
    source_id   = data.oci_core_images.ubuntu.images[0].id
  }

  metadata = {
    ssh_authorized_keys = var.ssh_public_key
    user_data = base64encode(templatefile("${path.module}/startup-script.sh", {
      signaling_image  = var.signaling_image
      media_server_url = var.media_server_url
      turn_server_ip   = var.turn_server_ip
      turn_username    = var.turn_username
      turn_password    = var.turn_password
      jwt_secret       = var.jwt_secret
    }))
  }

  freeform_tags = var.freeform_tags
}

# Get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

# Security list for signaling server
resource "oci_core_security_list" "signaling_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.name_prefix}-signaling-security-list"

  # Allow inbound HTTP traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 80
      max = 80
    }
  }

  # Allow inbound HTTPS traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 443
      max = 443
    }
  }

  # Allow inbound signaling server traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 3001
      max = 3001
    }
  }

  # Allow SSH
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 22
      max = 22
    }
  }

  # Allow all outbound traffic
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }

  freeform_tags = var.freeform_tags
}
