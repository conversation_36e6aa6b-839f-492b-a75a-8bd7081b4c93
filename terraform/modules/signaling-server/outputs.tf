# Outputs for OCI Signaling Server Module

output "instance_id" {
  description = "The OCID of the signaling server instance"
  value       = oci_core_instance.signaling_server.id
}

output "instance_name" {
  description = "The display name of the signaling server instance"
  value       = oci_core_instance.signaling_server.display_name
}

output "public_ip" {
  description = "The public IP address of the signaling server"
  value       = oci_core_instance.signaling_server.public_ip
}

output "private_ip" {
  description = "The private IP address of the signaling server"
  value       = oci_core_instance.signaling_server.private_ip
}

output "signaling_server_url" {
  description = "The URL of the signaling server"
  value       = "http://${oci_core_instance.signaling_server.public_ip}:3001"
}

output "security_list_id" {
  description = "The OCID of the signaling server security list"
  value       = oci_core_security_list.signaling_security_list.id
}
