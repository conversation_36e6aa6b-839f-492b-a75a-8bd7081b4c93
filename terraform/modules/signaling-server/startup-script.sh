#!/bin/bash

# Signaling Server Startup Script for OCI Ubuntu Instance
# This script installs Docker and runs the signaling server container

set -e

# Logging function
log_with_timestamp() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_with_timestamp "Starting signaling server setup..."

# Update system
log_with_timestamp "Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install Docker
log_with_timestamp "Installing Docker..."
apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
apt-get update -y
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Start and enable Docker
systemctl start docker
systemctl enable docker

# Add ubuntu user to docker group
usermod -aG docker ubuntu

log_with_timestamp "Docker installation completed"

# Create signaling server directory
mkdir -p /opt/signaling-server
cd /opt/signaling-server

# Create environment file
cat > .env << EOF
NODE_ENV=production
PORT=3001
MEDIA_SERVER_URL=${media_server_url}
TURN_SERVER_IP=${turn_server_ip}
TURN_USERNAME=${turn_username}
TURN_PASSWORD=${turn_password}
JWT_SECRET=${jwt_secret}
EOF

# Create docker-compose file
cat > docker-compose.yml << EOF
version: '3.8'
services:
  signaling-server:
    image: ${signaling_image}
    container_name: signaling-server
    restart: unless-stopped
    ports:
      - "3001:3001"
      - "80:3001"
      - "443:3001"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
EOF

# Create systemd service for signaling server
cat > /etc/systemd/system/signaling-server.service << EOF
[Unit]
Description=Signaling Server
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/signaling-server
ExecStart=/usr/bin/docker compose up -d
ExecStop=/usr/bin/docker compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable signaling-server.service

log_with_timestamp "Pulling signaling server image..."
docker pull ${signaling_image}

log_with_timestamp "Starting signaling server..."
systemctl start signaling-server.service

# Wait for service to be ready
log_with_timestamp "Waiting for signaling server to be ready..."
sleep 30

# Check if service is running
if systemctl is-active --quiet signaling-server.service; then
    log_with_timestamp "✅ Signaling server is running successfully"
    docker ps | grep signaling-server
else
    log_with_timestamp "❌ Signaling server failed to start"
    systemctl status signaling-server.service
    docker logs signaling-server
    exit 1
fi

log_with_timestamp "Signaling server setup completed successfully"
