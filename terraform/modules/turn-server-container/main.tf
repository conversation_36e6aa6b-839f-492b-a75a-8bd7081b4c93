# OCI TURN Server Container Instance Module for WebRTC Platform
# Deploys a TURN server using OCI Container Instance

# Get availability domains
data "oci_identity_availability_domains" "ads" {
  compartment_id = var.compartment_id
}

# Create Container Instance for TURN server
resource "oci_container_instances_container_instance" "turn_server" {
  availability_domain = data.oci_identity_availability_domains.ads.availability_domains[0].name
  compartment_id      = var.compartment_id
  display_name        = "${var.name_prefix}-turn-server"
  shape               = var.instance_shape

  shape_config {
    ocpus         = var.instance_ocpus
    memory_in_gbs = var.instance_memory_gb
  }

  vnics {
    subnet_id      = var.subnet_id
    display_name   = "${var.name_prefix}-turn-vnic"
    hostname_label = "turn"
    is_public_ip_assigned = true
  }

  containers {
    display_name = "turn-server"
    image_url    = var.turn_image

    environment_variables = {
      TURN_USERNAME = var.turn_username
      TURN_PASSWORD = var.turn_password
    }

    resource_config {
      memory_limit_in_gbs = var.container_memory_gb
      vcpus_limit         = var.container_vcpus
    }
  }

  freeform_tags = var.freeform_tags
}

# Security list for TURN server
resource "oci_core_security_list" "turn_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.name_prefix}-turn-security-list"

  # Allow STUN/TURN traffic (UDP)
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 3478
      max = 3478
    }
  }

  # Allow STUN/TURN traffic (TCP)
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 3478
      max = 3478
    }
  }

  # Allow TLS TURN traffic
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 5349
      max = 5349
    }
  }

  # Allow relay port range (UDP)
  ingress_security_rules {
    protocol = "17" # UDP
    source   = "0.0.0.0/0"
    udp_options {
      min = 49152
      max = 49252
    }
  }

  # Allow relay port range (TCP)
  ingress_security_rules {
    protocol = "6" # TCP
    source   = "0.0.0.0/0"
    tcp_options {
      min = 49152
      max = 49252
    }
  }

  # Allow all outbound traffic
  egress_security_rules {
    protocol    = "all"
    destination = "0.0.0.0/0"
  }

  freeform_tags = var.freeform_tags
}
