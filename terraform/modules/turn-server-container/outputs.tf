output "container_instance_id" {
  description = "ID of the TURN server container instance"
  value       = oci_container_instances_container_instance.turn_server.id
}

# Note: Container instances may not expose IP addresses directly
# We'll use the container instance ID and let users check IPs via OCI console
output "public_ip" {
  description = "Public IP address of the TURN server (check OCI console)"
  value       = "Check OCI Console for Container Instance: ${oci_container_instances_container_instance.turn_server.display_name}"
}

output "private_ip" {
  description = "Private IP address of the TURN server (check OCI console)"
  value       = "Check OCI Console for Container Instance: ${oci_container_instances_container_instance.turn_server.display_name}"
}

output "hostname" {
  description = "Hostname of the TURN server"
  value       = "${oci_container_instances_container_instance.turn_server.display_name}.${oci_container_instances_container_instance.turn_server.availability_domain}"
}
