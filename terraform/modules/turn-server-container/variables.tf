variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vcn_id" {
  description = "VCN ID"
  type        = string
}

variable "subnet_id" {
  description = "Subnet ID"
  type        = string
}

variable "instance_shape" {
  description = "Container instance shape"
  type        = string
  default     = "CI.Standard.E4.Flex"
}

variable "instance_ocpus" {
  description = "Number of OCPUs for the container instance"
  type        = number
  default     = 1
}

variable "instance_memory_gb" {
  description = "Memory in GB for the container instance"
  type        = number
  default     = 4
}

variable "container_vcpus" {
  description = "Number of vCPUs for the container"
  type        = number
  default     = 1
}

variable "container_memory_gb" {
  description = "Memory in GB for the container"
  type        = number
  default     = 2
}

variable "turn_image" {
  description = "Docker image for TURN server"
  type        = string
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  sensitive   = true
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
