# MongoDB Atlas Module Outputs

output "database_name" {
  description = "The name of the MongoDB database"
  value       = var.database_name
}

output "cluster_name" {
  description = "The name of the MongoDB Atlas cluster"
  value       = mongodbatlas_cluster.cluster.name
}

output "project_id" {
  description = "The MongoDB Atlas project ID"
  value       = mongodbatlas_project.project.id
}

output "connection_string" {
  description = "MongoDB connection string for Mongoose"
  value = replace(
    mongodbatlas_cluster.cluster.connection_strings[0].standard_srv,
    "mongodb+srv://",
    "mongodb+srv://${mongodbatlas_database_user.user.username}:${urlencode(random_password.db_password.result)}@"
  )
  sensitive = true
}

output "database_username" {
  description = "MongoDB database username"
  value       = mongodbatlas_database_user.user.username
}

output "database_password" {
  description = "MongoDB database password"
  value       = random_password.db_password.result
  sensitive   = true
}

output "config_file" {
  description = "Path to the MongoDB configuration file"
  value       = local_file.mongodb_config.filename
}
