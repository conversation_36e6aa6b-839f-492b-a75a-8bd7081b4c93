# OCI Load Balancer Module for Human-Readable Endpoints
# Creates load balancers with OCI-provided domain names for clean URLs

# Always create a load balancer for the signaling server for stable endpoint
# This will get a URL like: sai-platform-dev-signaling-lb.us-ashburn-1.oci.customer-oci.com
resource "oci_load_balancer_load_balancer" "signaling_server_lb" {
  compartment_id = var.compartment_id
  display_name   = "${var.name_prefix}-signaling-lb"
  shape          = "flexible"

  shape_details {
    minimum_bandwidth_in_mbps = 10
    maximum_bandwidth_in_mbps = 100
  }

  subnet_ids = [var.subnet_id]

  freeform_tags = var.freeform_tags
}

# Backend set for signaling server
resource "oci_load_balancer_backend_set" "signaling_backend" {
  name             = "signaling-backend"
  load_balancer_id = oci_load_balancer_load_balancer.signaling_server_lb.id
  policy           = "ROUND_ROBIN"

  health_checker {
    protocol          = "HTTP"
    interval_ms       = 10000
    port              = 3001
    response_body_regex = ".*"
    retries           = 3
    timeout_in_millis = 3000
    url_path          = "/health"
  }
}

# Note: Backend will be configured manually or via separate process
# Container instance IPs are not easily accessible from Terraform
# The load balancer provides the stable endpoint, backends can be added via OCI console

# Listener for signaling server HTTP traffic
resource "oci_load_balancer_listener" "signaling_listener" {
  load_balancer_id         = oci_load_balancer_load_balancer.signaling_server_lb.id
  name                     = "signaling-listener"
  default_backend_set_name = oci_load_balancer_backend_set.signaling_backend.name
  port                     = 80
  protocol                 = "HTTP"
}

# Create DNS zone (only if domain_name is provided)
resource "oci_dns_zone" "main" {
  count          = var.domain_name != "" ? 1 : 0
  compartment_id = var.compartment_id
  name           = var.domain_name
  zone_type      = "PRIMARY"

  freeform_tags = var.freeform_tags
}

# A record for signaling server (points to load balancer)
resource "oci_dns_rrset" "signaling_server" {
  count           = var.domain_name != "" ? 1 : 0
  zone_name_or_id = oci_dns_zone.main[0].id
  domain          = "api.${var.domain_name}"
  rtype           = "A"

  items {
    domain = "api.${var.domain_name}"
    rtype  = "A"
    rdata  = try(oci_load_balancer_load_balancer.signaling_server_lb.ip_address_details[0].ip_address, "*******")
    ttl    = 300
  }

  depends_on = [oci_dns_zone.main]
}

# A record for client app (pointing to a load balancer or CDN)
resource "oci_dns_rrset" "client_app" {
  count           = var.domain_name != "" && var.client_app_ip != "" ? 1 : 0
  zone_name_or_id = oci_dns_zone.main[0].id
  domain          = var.domain_name
  rtype           = "A"

  items {
    domain = var.domain_name
    rtype  = "A"
    rdata  = var.client_app_ip
    ttl    = 300
  }

  depends_on = [oci_dns_zone.main]
}

# CNAME record for www subdomain
resource "oci_dns_rrset" "www" {
  count           = var.domain_name != "" ? 1 : 0
  zone_name_or_id = oci_dns_zone.main[0].id
  domain          = "www.${var.domain_name}"
  rtype           = "CNAME"

  items {
    domain = "www.${var.domain_name}"
    rtype  = "CNAME"
    rdata  = var.domain_name
    ttl    = 300
  }

  depends_on = [oci_dns_zone.main]
}

# Create a load balancer for the client app (optional)
# This will get a URL like: sai-platform-dev-client-app-lb.us-ashburn-1.oci.customer-oci.com
resource "oci_load_balancer_load_balancer" "client_app_lb" {
  count          = var.create_load_balancer ? 1 : 0
  compartment_id = var.compartment_id
  display_name   = "${var.name_prefix}-client-app-lb"
  shape          = "flexible"

  shape_details {
    minimum_bandwidth_in_mbps = 10
    maximum_bandwidth_in_mbps = 100
  }

  subnet_ids = [var.subnet_id]

  freeform_tags = var.freeform_tags
}

# Backend set for client app (redirects to object storage)
resource "oci_load_balancer_backend_set" "client_app_backend" {
  count            = var.create_load_balancer ? 1 : 0
  name             = "client-app-backend"
  load_balancer_id = oci_load_balancer_load_balancer.client_app_lb[0].id
  policy           = "ROUND_ROBIN"

  health_checker {
    protocol          = "HTTP"
    interval_ms       = 30000
    port              = 443
    response_body_regex = ".*"
    retries           = 3
    timeout_in_millis = 5000
    url_path          = "/"
  }
}

# Listener for HTTP traffic (redirects to object storage)
resource "oci_load_balancer_listener" "client_app_listener" {
  count                    = var.create_load_balancer ? 1 : 0
  load_balancer_id         = oci_load_balancer_load_balancer.client_app_lb[0].id
  name                     = "client-app-listener"
  default_backend_set_name = oci_load_balancer_backend_set.client_app_backend[0].name
  port                     = 80
  protocol                 = "HTTP"

  # Add a rule to redirect to object storage
  rule_set_names = [oci_load_balancer_rule_set.client_app_redirect[0].name]
}

# Rule set to redirect to object storage
resource "oci_load_balancer_rule_set" "client_app_redirect" {
  count            = var.create_load_balancer ? 1 : 0
  load_balancer_id = oci_load_balancer_load_balancer.client_app_lb[0].id
  name             = "client-app-redirect"

  items {
    action = "REDIRECT"
    conditions {
      attribute_name  = "PATH"
      attribute_value = "/"
      operator        = "EXACT_MATCH"
    }
    redirect_uri {
      protocol = "HTTPS"
      host     = "objectstorage.${var.region}.oraclecloud.com"
      path     = "/n/${var.bucket_namespace}/b/${var.bucket_name}/o/index.html"
    }
    response_code = 302
  }
}
