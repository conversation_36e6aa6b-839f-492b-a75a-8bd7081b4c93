variable "compartment_id" {
  description = "OCI Compartment ID"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "domain_name" {
  description = "Domain name for DNS zone (leave empty to skip DNS setup)"
  type        = string
  default     = ""
}

# Note: signaling_server_ip removed - container instance IPs not easily accessible

variable "client_app_ip" {
  description = "IP address for client app (load balancer IP)"
  type        = string
  default     = ""
}

variable "subnet_id" {
  description = "Subnet ID for load balancer"
  type        = string
}

variable "region" {
  description = "OCI region for generating domain names"
  type        = string
}

variable "create_load_balancer" {
  description = "Whether to create a load balancer for the client app"
  type        = bool
  default     = false
}

variable "bucket_name" {
  description = "Name of the object storage bucket (for client app redirect)"
  type        = string
  default     = ""
}

variable "bucket_namespace" {
  description = "Namespace of the object storage bucket (for client app redirect)"
  type        = string
  default     = ""
}

variable "freeform_tags" {
  description = "Freeform tags to apply to resources"
  type        = map(string)
  default     = {}
}
