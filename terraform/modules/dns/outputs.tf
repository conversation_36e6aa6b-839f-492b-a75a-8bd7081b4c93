output "zone_id" {
  description = "ID of the DNS zone"
  value       = var.domain_name != "" ? oci_dns_zone.main[0].id : null
}

output "zone_name" {
  description = "Name of the DNS zone"
  value       = var.domain_name != "" ? oci_dns_zone.main[0].name : null
}

output "signaling_server_fqdn" {
  description = "FQDN for the signaling server (OCI-provided domain)"
  value       = "${oci_load_balancer_load_balancer.signaling_server_lb.display_name}.${var.region}.oci.customer-oci.com"
}

output "signaling_server_url" {
  description = "Full URL for the signaling server"
  value       = "http://${oci_load_balancer_load_balancer.signaling_server_lb.display_name}.${var.region}.oci.customer-oci.com"
}

output "signaling_server_ip" {
  description = "IP address of the signaling server load balancer"
  value       = try(oci_load_balancer_load_balancer.signaling_server_lb.ip_address_details[0].ip_address, "Check OCI Console")
}

output "client_app_fqdn" {
  description = "FQDN for the client app (OCI-provided domain)"
  value       = var.create_load_balancer ? "${oci_load_balancer_load_balancer.client_app_lb[0].display_name}.${var.region}.oci.customer-oci.com" : null
}

output "client_app_url" {
  description = "Full URL for the client app"
  value       = var.create_load_balancer ? "http://${oci_load_balancer_load_balancer.client_app_lb[0].display_name}.${var.region}.oci.customer-oci.com" : null
}

output "client_app_load_balancer_ip" {
  description = "IP address of the client app load balancer"
  value       = var.create_load_balancer ? try(oci_load_balancer_load_balancer.client_app_lb[0].ip_address_details[0].ip_address, "Check OCI Console") : null
}

output "nameservers" {
  description = "Nameservers for the DNS zone"
  value       = var.domain_name != "" ? oci_dns_zone.main[0].nameservers : []
}

output "oci_endpoints" {
  description = "OCI-provided endpoints (no external DNS setup required)"
  value = {
    signaling_server = {
      fqdn = "${oci_load_balancer_load_balancer.signaling_server_lb.display_name}.${var.region}.oci.customer-oci.com"
      url  = "http://${oci_load_balancer_load_balancer.signaling_server_lb.display_name}.${var.region}.oci.customer-oci.com"
      ip   = try(oci_load_balancer_load_balancer.signaling_server_lb.ip_address_details[0].ip_address, "Check OCI Console")
    }
    client_app = var.create_load_balancer ? {
      fqdn = "${oci_load_balancer_load_balancer.client_app_lb[0].display_name}.${var.region}.oci.customer-oci.com"
      url  = "http://${oci_load_balancer_load_balancer.client_app_lb[0].display_name}.${var.region}.oci.customer-oci.com"
      ip   = try(oci_load_balancer_load_balancer.client_app_lb[0].ip_address_details[0].ip_address, "Check OCI Console")
    } : null
  }
}

output "dns_setup_instructions" {
  description = "Instructions for setting up DNS"
  value = var.domain_name != "" ? {
    message = "To complete DNS setup, update your domain registrar to use these nameservers:"
    nameservers = oci_dns_zone.main[0].nameservers
    records = {
      signaling_server = "api.${var.domain_name} -> ${try(oci_load_balancer_load_balancer.signaling_server_lb.ip_address_details[0].ip_address, "Check OCI Console")}"
      client_app = var.create_load_balancer ? "${var.domain_name} -> ${try(oci_load_balancer_load_balancer.client_app_lb[0].ip_address_details[0].ip_address, "Check OCI Console")}" : "${var.domain_name} -> (manual setup required)"
    }
    oci_domains = null
  } : {
    message = "Using OCI-provided domains - no external DNS setup required!"
    nameservers = null
    records = null
    oci_domains = {
      signaling_server = "${oci_load_balancer_load_balancer.signaling_server_lb.display_name}.${var.region}.oci.customer-oci.com"
      client_app = var.create_load_balancer ? "${oci_load_balancer_load_balancer.client_app_lb[0].display_name}.${var.region}.oci.customer-oci.com" : "Use object storage URL directly"
    }
  }
}
