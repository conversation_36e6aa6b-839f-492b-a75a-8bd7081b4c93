# OCI Production Environment
# This configuration deploys the complete WebRTC platform to Oracle Cloud Infrastructure

terraform {
  required_version = ">= 1.0"

  required_providers {
    oci = {
      source  = "oracle/oci"
      version = "~> 5.0"
    }
  }

  # Configure remote state backend for production
  # backend "s3" {
  #   bucket = "your-terraform-state-bucket"
  #   key    = "webrtc-platform/prod/terraform.tfstate"
  #   region = "us-east-1"
  # }
}

# Configure OCI Provider
provider "oci" {
  tenancy_ocid     = var.tenancy_ocid
  user_ocid        = var.user_ocid
  fingerprint      = var.fingerprint
  private_key_path = var.private_key_path
  region           = var.region
}

# Local values
locals {
  environment = "prod"
  name_prefix = "${var.app_name}-${local.environment}"

  common_tags = {
    Environment = local.environment
    Application = var.app_name
    ManagedBy   = "terraform"
    Project     = "sai-platform"
  }
}

# Networking Module
module "networking" {
  source = "../../modules/networking"

  compartment_id = var.compartment_id
  vcn_name       = "${local.name_prefix}-vcn"
  vcn_cidr       = "10.2.0.0/16"
  subnet_name    = "${local.name_prefix}-subnet"
  subnet_cidr    = "10.2.1.0/24"

  freeform_tags = local.common_tags
}

# Security Module
module "security" {
  source = "../../modules/security"

  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix
  turn_username  = var.turn_username
  turn_password  = var.turn_password

  freeform_tags = local.common_tags
}

# Object Storage bucket for media server packages
resource "oci_objectstorage_bucket" "media_server_packages" {
  compartment_id = var.compartment_id
  name           = "sai-platform-prod-media-server-repo"
  namespace      = data.oci_objectstorage_namespace.current.namespace

  freeform_tags = local.common_tags
}

# Get current namespace
data "oci_objectstorage_namespace" "current" {
  compartment_id = var.compartment_id
}

# Media Server Module (production-sized)
module "media_server" {
  source = "../../modules/media-server"

  # OCI Configuration
  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix

  # Instance Configuration (larger for production)
  instance_shape     = var.instance_shape
  instance_ocpus     = var.instance_ocpus
  instance_memory_gb = var.instance_memory_gb
  ssh_public_key     = var.ssh_public_key

  # Application Configuration
  package_name    = var.package_name
  package_version = var.package_version
  jwt_secret      = var.jwt_secret

  # Networking - use shared networking
  vcn_id    = module.networking.vcn_id
  subnet_id = module.networking.subnet_id

  # Tags
  freeform_tags = local.common_tags

  depends_on = [module.networking]
}

# Signaling Server Module (production-sized)
module "signaling_server" {
  source = "../../modules/signaling-server"

  compartment_id     = var.compartment_id
  name_prefix        = local.name_prefix
  vcn_id             = module.networking.vcn_id
  subnet_id          = module.networking.subnet_id
  instance_shape     = var.signaling_instance_shape
  instance_ocpus     = var.signaling_instance_ocpus
  instance_memory_gb = var.signaling_instance_memory_gb
  ssh_public_key     = var.ssh_public_key
  signaling_image    = var.signaling_image
  media_server_url   = module.media_server.media_server_url
  turn_server_ip     = module.turn_server.public_ip
  turn_username      = var.turn_username
  turn_password      = var.turn_password
  jwt_secret         = var.jwt_secret

  freeform_tags = local.common_tags

  depends_on = [module.networking, module.media_server, module.turn_server]
}

# TURN Server Module (production-sized)
module "turn_server" {
  source = "../../modules/turn-server"

  compartment_id     = var.compartment_id
  name_prefix        = local.name_prefix
  vcn_id             = module.networking.vcn_id
  subnet_id          = module.networking.subnet_id
  instance_shape     = var.turn_instance_shape
  instance_ocpus     = var.turn_instance_ocpus
  instance_memory_gb = var.turn_instance_memory_gb
  ssh_public_key     = var.ssh_public_key
  turn_image         = var.turn_image
  turn_username      = var.turn_username
  turn_password      = var.turn_password

  freeform_tags = local.common_tags

  depends_on = [module.networking]
}
