# OCI Production Environment Variables

# OCI Provider Configuration
variable "tenancy_ocid" {
  description = "OCI Tenancy OCID"
  type        = string
}

variable "user_ocid" {
  description = "OCI User OCID"
  type        = string
}

variable "fingerprint" {
  description = "OCI API Key Fingerprint"
  type        = string
}

variable "private_key_path" {
  description = "Path to OCI API private key file"
  type        = string
}

variable "region" {
  description = "OCI Region"
  type        = string
  default     = "us-ashburn-1"
}

variable "compartment_id" {
  description = "OCI Compartment OCID"
  type        = string
}

# Application Configuration
variable "app_name" {
  description = "Application name"
  type        = string
  default     = "sai-platform"
}

# Media Server Configuration (production-sized but efficient)
variable "instance_ocpus" {
  description = "Number of OCPUs for the media server instance"
  type        = number
  default     = 1 # Small even for production
}

variable "instance_memory_gb" {
  description = "Memory in GB for the media server instance"
  type        = number
  default     = 6 # Small even for production
}

variable "instance_shape" {
  description = "Instance shape for media server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

# Package Configuration
variable "package_name" {
  description = "Name of the media server package"
  type        = string
  default     = "media-server"
}

variable "package_version" {
  description = "Version of the media server package"
  type        = string
  default     = "1.0.10"
}

variable "jwt_secret" {
  description = "JWT secret key for media server authentication"
  type        = string
  sensitive   = true
}

# Signaling Server Configuration (production-sized for high concurrency)
variable "signaling_instance_shape" {
  description = "Instance shape for signaling server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "signaling_instance_ocpus" {
  description = "Number of OCPUs for signaling server"
  type        = number
  default     = 4 # Larger for production concurrency
}

variable "signaling_instance_memory_gb" {
  description = "Memory in GB for signaling server"
  type        = number
  default     = 24 # Larger for production concurrency
}

variable "signaling_image" {
  description = "Docker image for signaling server"
  type        = string
}

# TURN Server Configuration (small even for production)
variable "turn_instance_shape" {
  description = "Instance shape for TURN server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "turn_instance_ocpus" {
  description = "Number of OCPUs for TURN server"
  type        = number
  default     = 1 # Small even for production
}

variable "turn_instance_memory_gb" {
  description = "Memory in GB for TURN server"
  type        = number
  default     = 6 # Small even for production
}

variable "turn_image" {
  description = "Docker image for TURN server"
  type        = string
  default     = "coturn/coturn:latest"
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  sensitive   = true
}
