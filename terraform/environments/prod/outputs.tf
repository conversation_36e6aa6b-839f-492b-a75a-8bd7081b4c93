# OCI Production Environment Outputs

# Networking Outputs
output "vcn_id" {
  description = "OCID of the VCN"
  value       = module.networking.vcn_id
}

output "subnet_id" {
  description = "OCID of the subnet"
  value       = module.networking.subnet_id
}

# Media Server Outputs
output "media_server_public_ip" {
  description = "Public IP address of the media server"
  value       = module.media_server.media_server_public_ip
}

output "media_server_private_ip" {
  description = "Private IP address of the media server"
  value       = module.media_server.media_server_private_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.media_server.media_server_url
}

output "media_server_instance_id" {
  description = "OCID of the media server instance"
  value       = module.media_server.instance_id
}

# Signaling Server Outputs
output "signaling_server_public_ip" {
  description = "Public IP address of the signaling server"
  value       = module.signaling_server.public_ip
}

output "signaling_server_private_ip" {
  description = "Private IP address of the signaling server"
  value       = module.signaling_server.private_ip
}

output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = module.signaling_server.signaling_server_url
}

output "signaling_server_instance_id" {
  description = "OCID of the signaling server instance"
  value       = module.signaling_server.instance_id
}

# TURN Server Outputs
output "turn_server_public_ip" {
  description = "Public IP address of the TURN server"
  value       = module.turn_server.public_ip
}

output "turn_server_private_ip" {
  description = "Private IP address of the TURN server"
  value       = module.turn_server.private_ip
}

output "turn_server_instance_id" {
  description = "OCID of the TURN server instance"
  value       = module.turn_server.instance_id
}

# Security Outputs
output "vault_id" {
  description = "OCID of the vault"
  value       = module.security.vault_id
}

# SSH Commands
output "ssh_commands" {
  description = "SSH commands to connect to instances"
  value = {
    media_server     = "ssh ubuntu@${module.media_server.media_server_public_ip}"
    signaling_server = "ssh ubuntu@${module.signaling_server.public_ip}"
    turn_server      = "ssh ubuntu@${module.turn_server.public_ip}"
  }
}

# Deployment Information
output "deployment_info" {
  description = "Production deployment information"
  value = {
    environment = local.environment
    region      = var.region
    media_server = {
      instance_shape = var.instance_shape
      ocpus          = var.instance_ocpus
      memory_gb      = var.instance_memory_gb
      package        = "${var.package_name}-${var.package_version}.tgz"
      architecture   = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.instance_shape) ? "ARM64" : "x86_64"
    }
    signaling_server = {
      instance_shape = var.signaling_instance_shape
      ocpus          = var.signaling_instance_ocpus
      memory_gb      = var.signaling_instance_memory_gb
    }
    turn_server = {
      instance_shape = var.turn_instance_shape
      ocpus          = var.turn_instance_ocpus
      memory_gb      = var.turn_instance_memory_gb
    }
  }
}

# Environment Configuration for Applications
output "environment_config" {
  description = "Environment variables for application deployment"
  sensitive   = true
  value = {
    MEDIA_SERVER_URL     = module.media_server.media_server_url
    SIGNALING_SERVER_URL = module.signaling_server.signaling_server_url
    TURN_SERVER_IP       = module.turn_server.public_ip
    TURN_USERNAME        = var.turn_username
    TURN_PASSWORD        = var.turn_password
    JWT_SECRET           = var.jwt_secret
  }
}
