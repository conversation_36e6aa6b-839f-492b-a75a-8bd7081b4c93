#!/bin/bash

# OCI Media Server Deployment Script
# Usage: ./deploy.sh [action]
# Actions: plan, apply, destroy, output

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed. Please install Terraform first."
        exit 1
    fi
    
    # Check if OCI CLI is installed
    if ! command -v oci &> /dev/null; then
        log_error "OCI CLI is not installed. Please install OCI CLI first."
        exit 1
    fi
    
    # Check if OCI CLI is configured
    if ! oci iam user get --user-id "$(oci iam user list --query 'data[0].id' --raw-output 2>/dev/null)" &> /dev/null; then
        log_error "OCI CLI is not configured. Please run 'oci setup config' first."
        exit 1
    fi
    
    # Check if terraform.tfvars exists
    if [ ! -f "terraform.tfvars" ]; then
        log_error "terraform.tfvars not found. Please copy terraform.tfvars.example and configure it."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Initialize Terraform
init_terraform() {
    log_info "Initializing Terraform..."
    terraform init
    log_success "Terraform initialized"
}

# Plan deployment
plan_deployment() {
    log_info "Planning deployment..."
    terraform plan -out=tfplan
    log_success "Deployment plan created"
}

# Apply deployment
apply_deployment() {
    log_info "Applying deployment..."
    if [ -f "tfplan" ]; then
        terraform apply tfplan
        rm -f tfplan
    else
        terraform apply
    fi
    log_success "Deployment completed"
}

# Show outputs
show_outputs() {
    log_info "Deployment outputs:"
    terraform output
    
    # Show connection information
    if terraform output media_server_public_ip &> /dev/null; then
        PUBLIC_IP=$(terraform output -raw media_server_public_ip)
        log_info "Media Server URL: https://${PUBLIC_IP}:8080"
        log_info "SSH Command: ssh opc@${PUBLIC_IP}"
        log_info "Check service: ssh opc@${PUBLIC_IP} 'sudo systemctl status media-server'"
    fi
}

# Destroy deployment
destroy_deployment() {
    log_warning "This will destroy all resources. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Destroying deployment..."
        terraform destroy
        log_success "Deployment destroyed"
    else
        log_info "Destroy cancelled"
    fi
}

# Validate deployment
validate_deployment() {
    log_info "Validating deployment..."
    
    if ! terraform output media_server_public_ip &> /dev/null; then
        log_error "Deployment validation failed - no public IP found"
        return 1
    fi
    
    PUBLIC_IP=$(terraform output -raw media_server_public_ip)
    log_info "Testing connectivity to ${PUBLIC_IP}..."
    
    # Test SSH connectivity
    if timeout 10 nc -z "${PUBLIC_IP}" 22; then
        log_success "SSH port (22) is accessible"
    else
        log_warning "SSH port (22) is not accessible yet"
    fi
    
    # Test media server port
    if timeout 10 nc -z "${PUBLIC_IP}" 8080; then
        log_success "Media server port (8080) is accessible"
    else
        log_warning "Media server port (8080) is not accessible yet (may still be starting)"
    fi
    
    log_info "Validation completed"
}

# Main function
main() {
    local action=${1:-"apply"}
    
    log_info "OCI Media Server Deployment"
    log_info "Action: ${action}"
    
    case $action in
        "plan")
            check_prerequisites
            init_terraform
            plan_deployment
            ;;
        "apply")
            check_prerequisites
            init_terraform
            plan_deployment
            apply_deployment
            show_outputs
            validate_deployment
            ;;
        "destroy")
            check_prerequisites
            destroy_deployment
            ;;
        "output")
            show_outputs
            ;;
        "validate")
            validate_deployment
            ;;
        *)
            log_error "Unknown action: ${action}"
            log_info "Usage: $0 [plan|apply|destroy|output|validate]"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
