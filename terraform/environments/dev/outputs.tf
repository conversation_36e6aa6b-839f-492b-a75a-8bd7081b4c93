# Networking Outputs
output "vcn_id" {
  description = "OCID of the VCN"
  value       = module.networking.vcn_id
}

output "subnet_id" {
  description = "OCID of the subnet"
  value       = module.networking.subnet_id
}

# Media Server Outputs
output "media_server_public_ip" {
  description = "Public IP address of the media server"
  value       = module.media_server.media_server_public_ip
}

output "media_server_private_ip" {
  description = "Private IP address of the media server"
  value       = module.media_server.media_server_private_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.media_server.media_server_url
}

output "media_server_instance_id" {
  description = "OCID of the media server instance"
  value       = module.media_server.instance_id
}

# Signaling Server Outputs
output "signaling_server_public_ip" {
  description = "Public IP address of the signaling server"
  value       = module.signaling_server.public_ip
}

output "signaling_server_private_ip" {
  description = "Private IP address of the signaling server"
  value       = module.signaling_server.private_ip
}

output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = module.signaling_server.signaling_server_url
}

output "signaling_server_container_instance_id" {
  description = "OCID of the signaling server container instance"
  value       = module.signaling_server.container_instance_id
}

# TURN Server Outputs
output "turn_server_public_ip" {
  description = "Public IP address of the TURN server"
  value       = module.turn_server.public_ip
}

output "turn_server_private_ip" {
  description = "Private IP address of the TURN server"
  value       = module.turn_server.private_ip
}

output "turn_server_container_instance_id" {
  description = "OCID of the TURN server container instance"
  value       = module.turn_server.container_instance_id
}

# Container Registry Outputs
output "container_registry_urls" {
  description = "Container registry repository URLs"
  value = {
    signaling_server = module.container_registry.signaling_server_repo_url
    turn_server      = module.container_registry.turn_server_repo_url
  }
}

# Client App Outputs
output "client_app_url" {
  description = "Public URL for the client application"
  value       = module.client_app_storage.public_url
}

output "client_app_bucket" {
  description = "Client app storage bucket information"
  value = {
    name      = module.client_app_storage.bucket_name
    namespace = module.client_app_storage.bucket_namespace
    url       = module.client_app_storage.bucket_url
  }
}

# SSH Commands (only for media server - containers don't support SSH)
output "ssh_commands" {
  description = "SSH commands to connect to instances"
  value = {
    media_server = "ssh ubuntu@${module.media_server.media_server_public_ip}"
  }
}

# Deployment Information
output "deployment_info" {
  description = "Deployment information"
  value = {
    environment = local.environment
    region      = var.region
    media_server = {
      instance_shape = var.instance_shape
      ocpus          = var.instance_ocpus
      memory_gb      = var.instance_memory_gb
      package        = "${var.package_name}-${var.package_version}.tgz"
      architecture   = contains(["VM.Standard.A1.Flex", "VM.Standard.A2.Flex"], var.instance_shape) ? "ARM64" : "x86_64"
    }
    signaling_server = {
      instance_shape = var.signaling_instance_shape
      ocpus          = var.signaling_instance_ocpus
      memory_gb      = var.signaling_instance_memory_gb
      deployment     = "container_instance"
    }
    turn_server = {
      instance_shape = var.turn_instance_shape
      ocpus          = var.turn_instance_ocpus
      memory_gb      = var.turn_instance_memory_gb
      deployment     = "container_instance"
    }
    client_app = {
      deployment = "object_storage"
      url        = module.client_app_storage.public_url
    }
  }
}

# OCI Load Balancer Endpoints (human-readable URLs)
output "oci_endpoints" {
  description = "OCI-provided human-readable endpoints"
  value = {
    signaling_server = {
      fqdn = module.load_balancers.signaling_server_fqdn
      url  = module.load_balancers.signaling_server_url
      ip   = module.load_balancers.signaling_server_ip
    }
    client_app = var.create_load_balancer ? {
      fqdn = module.load_balancers.client_app_fqdn
      url  = module.load_balancers.client_app_url
      ip   = module.load_balancers.client_app_load_balancer_ip
    } : {
      url = module.client_app_storage.public_url
    }
  }
}

# DNS Setup Instructions
output "dns_setup_info" {
  description = "DNS setup information and instructions"
  value       = module.load_balancers.dns_setup_instructions
}

# Summary of all endpoints
output "endpoint_summary" {
  description = "Summary of all available endpoints"
  value = {
    signaling_server = {
      load_balancer_url = module.load_balancers.signaling_server_url
      direct_ip_url     = module.signaling_server.signaling_server_url
      description       = "Use load_balancer_url for production (stable endpoint)"
    }
    client_app = {
      load_balancer_url = var.create_load_balancer ? module.load_balancers.client_app_url : null
      object_storage_url = module.client_app_storage.public_url
      description       = var.create_load_balancer ? "Use load_balancer_url for custom domain" : "Use object_storage_url directly"
    }
    media_server = {
      url = module.media_server.media_server_url
      description = "Direct connection to media server"
    }
  }
}

# Docker Build Outputs
output "docker_images" {
  description = "Built Docker images information"
  value = {
    signaling_server_image = module.docker_build.signaling_server_image_url
    turn_server_image      = module.docker_build.turn_server_image_url
    build_scripts          = module.docker_build.build_scripts
    build_status           = module.docker_build.build_status
  }
}

# Container Registry Outputs
output "container_registry" {
  description = "Container registry information"
  value = {
    signaling_server_repo = module.container_registry.signaling_server_repo_url
    turn_server_repo      = module.container_registry.turn_server_repo_url
    namespace             = module.container_registry.registry_namespace
  }
}
