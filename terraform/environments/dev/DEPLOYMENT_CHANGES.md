# SAI Platform Dev Environment - Deployment Changes

This document outlines the major changes made to the dev environment Terraform configuration to modernize the deployment architecture.

## Summary of Changes

### 1. ✅ Removed Vault Infrastructure
- **Removed**: OCI Vault, KMS keys, and vault secrets
- **Reason**: Simplified architecture, vault was not needed for this use case
- **Impact**: Reduced complexity and cost

### 2. ✅ Container Registry for Docker Images
- **Added**: OCI Container Registry module
- **Purpose**: Store Docker images for signaling and TURN servers
- **Repositories**: 
  - `{namespace}.ocir.io/sai-platform-dev/signaling-server`
  - `{namespace}.ocir.io/sai-platform-dev/turn-server`

### 3. ✅ Container Instances (No More VMs)
- **Changed**: Signaling and TURN servers now use OCI Container Instances
- **Benefits**: 
  - Faster deployment
  - Better resource utilization
  - Automatic scaling capabilities
  - No SSH access needed (logs via OCI console)

### 4. ✅ MongoDB Configuration in Environment Variables
- **Changed**: MongoDB config now passed as environment variable `MONGODB_URI`
- **Removed**: Static `mongodb-config.json` file dependency
- **Approach**: Use external MongoDB (not managed by Terraform)

### 5. ✅ Client App Object Storage
- **Added**: Public OCI Object Storage bucket for React client app
- **Features**:
  - Publicly accessible
  - Automatic build and deploy process
  - Configuration injection at build time

### 6. ✅ Human-Readable OCI Domain Names
- **Added**: Load balancers with OCI-provided domain names
- **URLs**: 
  - Signaling Server: `sai-platform-dev-signaling-lb.{region}.oci.customer-oci.com`
  - Client App: `sai-platform-dev-client-app-lb.{region}.oci.customer-oci.com`
- **Benefits**: No external DNS management required

## New Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        OCI Dev Environment                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Load Balancer │    │  Container       │                   │
│  │   (Signaling)   │───▶│  Instance        │                   │
│  │   *.oci.com     │    │  (Signaling)     │                   │
│  └─────────────────┘    └──────────────────┘                   │
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Load Balancer │    │  Container       │                   │
│  │   (Client App)  │───▶│  Instance        │                   │
│  │   *.oci.com     │    │  (TURN Server)   │                   │
│  └─────────────────┘    └──────────────────┘                   │
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Object        │    │  VM Instance     │                   │
│  │   Storage       │    │  (Media Server)  │                   │
│  │   (Client App)  │    │  (Still VM)      │                   │
│  └─────────────────┘    └──────────────────┘                   │
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Container     │    │  MongoDB         │                   │
│  │   Registry      │    │  (External -     │                   │
│  │   (Images)      │    │   Not in TF)     │                   │
│  └─────────────────┘    └──────────────────┘                   │
└─────────────────────────────────────────────────────────────────┘
```

## Required Configuration

### 1. Update terraform.tfvars

Add these new variables to your `terraform.tfvars`:

```hcl
# MongoDB Configuration (configure externally)
mongodb_uri = "mongodb+srv://username:<EMAIL>/database"

# Container Images
signaling_image_tag = "latest"
turn_image_tag      = "latest"

# DNS Configuration (recommended settings)
domain_name           = ""     # Leave empty for OCI domains
create_load_balancer  = true   # Creates stable OCI domain endpoints
```

### 2. ✅ Automated Docker Image Building

**Docker images are now built automatically by Terraform!** No manual building required.

The new `docker-build` module:
- Automatically builds signaling server and TURN server images
- Pushes them to OCI Container Registry
- Rebuilds only when source code changes
- Provides build scripts for manual use if needed

**Manual build option** (if needed):
```bash
# Terraform generates these scripts automatically
./build-all-images.sh           # Build both images
./build-signaling-server.sh     # Build signaling server only
./build-turn-server.sh          # Build TURN server only
```

## Deployment Process

1. **Configure Variables**: Update `terraform.tfvars` with your values
2. **Deploy Infrastructure**: Run `terraform apply` (images build automatically!)
3. **Access Applications**: Use the OCI-provided domain names

**That's it!** Terraform handles everything including:
- Creating container registry
- Building and pushing Docker images
- Deploying container instances
- Setting up load balancers
- Configuring networking

## Accessing Your Applications

After deployment, you'll get these endpoints:

- **Signaling Server**: `http://sai-platform-dev-signaling-lb.{region}.oci.customer-oci.com`
- **Client App**: `http://sai-platform-dev-client-app-lb.{region}.oci.customer-oci.com`
- **Object Storage**: Direct URL for client app files

## Benefits of New Architecture

1. **Fully Automated**: Single `terraform apply` deploys everything
2. **Simplified Management**: No vault, fewer moving parts
3. **Better Scalability**: Container instances scale automatically
4. **Cost Effective**: Pay only for what you use
5. **Human-Readable URLs**: Clean, predictable domain names
6. **No External DNS**: OCI provides domain names automatically
7. **Faster Deployments**: Containers start faster than VMs
8. **Better Monitoring**: OCI console provides container logs and metrics
9. **Smart Rebuilds**: Images only rebuild when source code changes

## Migration Notes

- **No SSH Access**: Container instances don't support SSH (use OCI console for logs)
- **Image Management**: Need to build and push images before deployment
- **MongoDB**: Now requires external MongoDB URI configuration
- **Load Balancer Costs**: Small additional cost for load balancers (~$20/month)

## Next Steps

1. Set up CI/CD pipeline for automatic image builds
2. Configure SSL certificates for HTTPS endpoints
3. Set up monitoring and alerting
4. Consider multi-region deployment for production
