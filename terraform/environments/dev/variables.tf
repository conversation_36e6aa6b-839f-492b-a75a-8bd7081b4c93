# OCI Provider Configuration
variable "tenancy_ocid" {
  description = "OCI Tenancy OCID"
  type        = string
}

variable "user_ocid" {
  description = "OCI User OCID"
  type        = string
}

variable "fingerprint" {
  description = "OCI API Key Fingerprint"
  type        = string
}

variable "private_key_path" {
  description = "Path to OCI API private key file"
  type        = string
}

variable "region" {
  description = "OCI Region"
  type        = string
  default     = "us-ashburn-1"
}

variable "auth_type" {
  description = "OCI Authentication type (SecurityToken for session auth, ApiKey for API key auth)"
  type        = string
  default     = "SecurityToken"
}

variable "config_file_profile" {
  description = "OCI Config file profile name"
  type        = string
  default     = "mike"
}

variable "compartment_id" {
  description = "OCI Compartment OCID"
  type        = string
}

# Application Configuration
variable "app_name" {
  description = "Application name"
  type        = string
  default     = "sai-platform"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# Media Server Instance Configuration (small for dev)
variable "instance_ocpus" {
  description = "Number of OCPUs for the media server instance"
  type        = number
  default     = 1
}

variable "instance_memory_gb" {
  description = "Memory in GB for the media server instance"
  type        = number
  default     = 6
}

variable "instance_shape" {
  description = "Instance shape for media server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

# Package Configuration
variable "package_name" {
  description = "Name of the media server package"
  type        = string
  default     = "media-server"
}

variable "package_version" {
  description = "Version of the media server package"
  type        = string
  default     = "1.0.0"
}

variable "jwt_secret" {
  description = "JWT secret key for media server authentication"
  type        = string
  sensitive   = true
  default     = "your-secret-key-change-in-production"
}

# Signaling Server Configuration (larger for dev testing)
variable "signaling_instance_shape" {
  description = "Instance shape for signaling server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "signaling_instance_ocpus" {
  description = "Number of OCPUs for signaling server"
  type        = number
  default     = 2
}

variable "signaling_instance_memory_gb" {
  description = "Memory in GB for signaling server"
  type        = number
  default     = 12
}

variable "signaling_image_tag" {
  description = "Docker image tag for signaling server"
  type        = string
  default     = "latest"
}

# TURN Server Configuration (small for dev)
variable "turn_instance_shape" {
  description = "Instance shape for TURN server"
  type        = string
  default     = "VM.Standard.A2.Flex"
}

variable "turn_instance_ocpus" {
  description = "Number of OCPUs for TURN server"
  type        = number
  default     = 1
}

variable "turn_instance_memory_gb" {
  description = "Memory in GB for TURN server"
  type        = number
  default     = 6
}

variable "turn_image_tag" {
  description = "Docker image tag for TURN server"
  type        = string
  default     = "latest"
}

variable "force_rebuild_images" {
  description = "Force rebuild of Docker images even if no changes detected"
  type        = bool
  default     = false
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
  default     = "turnuser"
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  sensitive   = true
  default     = "changeme"
}

# MongoDB Configuration
variable "mongodb_uri" {
  description = "MongoDB connection URI (configure externally)"
  type        = string
  sensitive   = true
}

# Networking (Optional - now deprecated as we create our own)
variable "vcn_id" {
  description = "Existing VCN OCID (optional - will create new VCN if not provided)"
  type        = string
  default     = null
}

variable "subnet_id" {
  description = "Existing subnet OCID (optional - will create new subnet if not provided)"
  type        = string
  default     = null
}

# DNS Configuration (Optional)
variable "domain_name" {
  description = "Domain name for DNS setup (leave empty to skip DNS configuration)"
  type        = string
  default     = ""
}

variable "create_load_balancer" {
  description = "Whether to create a load balancer for the client app"
  type        = bool
  default     = false
}
