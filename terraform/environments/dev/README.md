# SAI Platform - Dev Environment

This is the development environment for the SAI Platform WebRTC streaming application, deployed on Oracle Cloud Infrastructure (OCI).

## 🚀 Quick Start

### Prerequisites

1. **OCI CLI** installed and configured
2. **Docker** installed and running
3. **Terraform** installed (v1.0+)

### Deploy Everything

1. **Configure your environment**:
   ```bash
   cd terraform/environments/dev
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your values
   ```

2. **Deploy with one command**:
   ```bash
   terraform init
   terraform apply
   ```

That's it! Terraform will:
- ✅ Create OCI Container Registry
- ✅ Build and push Docker images automatically
- ✅ Deploy signaling server (container instance)
- ✅ Deploy TURN server (container instance)
- ✅ Deploy media server (VM instance)
- ✅ Create object storage for client app
- ✅ Build and deploy React client app
- ✅ Set up load balancers with human-readable URLs
- ✅ Configure networking and security

## 🌐 Access Your Applications

After deployment, you'll get OCI-provided domain names like:

- **Signaling Server**: `http://sai-platform-dev-signaling-lb.us-ashburn-1.oci.customer-oci.com`
- **Client App**: `http://sai-platform-dev-client-app-lb.us-ashburn-1.oci.customer-oci.com`
- **Object Storage**: Direct URL for static files

## 📋 Configuration

### Required Variables

```hcl
# Basic Configuration
compartment_id = "ocid1.compartment.oc1..your-compartment-id"
region         = "us-ashburn-1"

# MongoDB (configure externally)
mongodb_uri = "mongodb+srv://username:<EMAIL>/database"

# Container Images (automatically built)
signaling_image_tag = "latest"
turn_image_tag      = "latest"

# DNS Configuration (recommended)
domain_name          = ""     # Leave empty for OCI domains
create_load_balancer = true   # Creates stable endpoints
```

### Optional Variables

```hcl
# Force rebuild images
force_rebuild_images = true   # Set to true to force rebuild

# Custom domain (if you have one)
domain_name = "yourdomain.com"
```

## 🔧 Manual Operations

### Rebuild Images Only

If you need to rebuild images without full deployment:

```bash
# Terraform generates these scripts
./build-all-images.sh           # Build both images
./build-signaling-server.sh     # Build signaling server only
./build-turn-server.sh          # Build TURN server only
```

### View Deployment Status

```bash
terraform output
```

### Clean Up

```bash
terraform destroy
```

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        OCI Dev Environment                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Load Balancer │    │  Container       │                   │
│  │   (Signaling)   │───▶│  Instance        │                   │
│  │   *.oci.com     │    │  (Signaling)     │                   │
│  └─────────────────┘    └──────────────────┘                   │
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Load Balancer │    │  Container       │                   │
│  │   (Client App)  │───▶│  Instance        │                   │
│  │   *.oci.com     │    │  (TURN Server)   │                   │
│  └─────────────────┘    └──────────────────┘                   │
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Object        │    │  VM Instance     │                   │
│  │   Storage       │    │  (Media Server)  │                   │
│  │   (Client App)  │    │  (mediasoup)     │                   │
│  └─────────────────┘    └──────────────────┘                   │
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐                   │
│  │   Container     │    │  MongoDB         │                   │
│  │   Registry      │    │  (External)      │                   │
│  │   (Images)      │    │                  │                   │
│  └─────────────────┘    └──────────────────┘                   │
└─────────────────────────────────────────────────────────────────┘
```

## 🔍 Troubleshooting

### Image Build Issues

1. **Docker authentication**: Make sure you can run `oci session authenticate`
2. **Docker daemon**: Ensure Docker is running
3. **Source code changes**: Images only rebuild when source code changes

### Container Instance Issues

1. **Check logs**: Use OCI Console → Container Instances → Logs
2. **Health checks**: Containers have built-in health checks
3. **Environment variables**: Verify MongoDB URI and other configs

### Load Balancer Issues

1. **Backend health**: Check if container instances are healthy
2. **Security lists**: Ensure ports 80/443 are open
3. **DNS propagation**: OCI domains are immediate, no propagation needed

## 📚 More Information

- [DEPLOYMENT_CHANGES.md](./DEPLOYMENT_CHANGES.md) - Detailed changes from previous architecture
- [terraform.tfvars.example](./terraform.tfvars.example) - All configuration options
- [OCI Documentation](https://docs.oracle.com/en-us/iaas/Content/home.htm)

## 🎯 Key Benefits

- **One Command Deployment**: `terraform apply` does everything
- **No Manual Docker Builds**: Images build automatically
- **Human-Readable URLs**: Clean OCI domain names
- **No External DNS**: No domain management required
- **Smart Rebuilds**: Only rebuilds when code changes
- **Container-Based**: Fast, scalable, cost-effective
