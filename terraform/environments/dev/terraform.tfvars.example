# OCI Development Environment Variables
# Copy this file to terraform.tfvars and update with your values

# OCI Provider Configuration
tenancy_ocid     = "ocid1.tenancy.oc1..aaaaaaaa3zo55xe333wrrnju5agpybkwv7t7tuuiovd4dgqg72ahjskra4ma"
user_ocid        = "ocid1.user.oc1..aaaaaaaagja627i3iql26afptos4isweheblawg6cj3tro3jmmdjrkh436iq"
fingerprint      = "72:45:fb:5a:31:06:b4:48:05:3f:1b:e6:fc:d0:6d:c1"
private_key_path = "~/.oci/oci_api_key.pem"
region           = "us-ashburn-1"
compartment_id   = "ocid1.tenancy.oc1..aaaaaaaa3zo55xe333wrrnju5agpybkwv7t7tuuiovd4dgqg72ahjskra4ma"

# Application Configuration
app_name = "sai-platform"

# Media Server Instance Configuration (Ampere A2 - ARM64)
instance_ocpus     = 1    # Small for dev
instance_memory_gb = 6    # Small for dev

# Signaling Server Configuration (larger for concurrency testing)
signaling_instance_ocpus     = 2
signaling_instance_memory_gb = 12

# TURN Server Configuration (small for dev)
turn_instance_ocpus     = 1
turn_instance_memory_gb = 6

# SSH Configuration
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... your-public-key"

# Package Configuration
package_name    = "media-server"
package_version = "1.0.10"

# MongoDB Configuration (configure externally)
mongodb_uri = "mongodb+srv://username:<EMAIL>/database"

# Container Images (automatically built and pushed by Terraform)
signaling_image_tag   = "latest"
turn_image_tag        = "latest"
force_rebuild_images  = false  # Set to true to force rebuild even if no changes detected

# DNS Configuration
# Leave domain_name empty to use OCI-provided domains (recommended)
# Set create_load_balancer = true for stable endpoints like:
#   - sai-platform-dev-signaling-lb.us-ashburn-1.oci.customer-oci.com
#   - sai-platform-dev-client-app-lb.us-ashburn-1.oci.customer-oci.com
domain_name           = ""     # Leave empty for OCI domains, or set to "yourdomain.com" for custom
create_load_balancer  = true   # Recommended: creates stable OCI domain endpoints

# Networking (Optional - leave empty to create new VCN/subnet)
vcn_id    = null
subnet_id = null
