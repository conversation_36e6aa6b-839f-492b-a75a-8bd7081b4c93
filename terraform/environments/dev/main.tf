# OCI Development Environment
# This configuration deploys the complete WebRTC platform to Oracle Cloud Infrastructure

terraform {
  required_version = ">= 1.0"

  required_providers {
    oci = {
      source  = "oracle/oci"
      version = "~> 5.0"
    }
  }
}

# Configure OCI Provider
provider "oci" {
  auth                = "SecurityToken"
  config_file_profile = "mike"
  region              = var.region
}

# Local values
locals {
  environment = "dev"
  name_prefix = "${var.app_name}-${local.environment}"

  common_tags = {
    Environment = local.environment
    Application = var.app_name
    ManagedBy   = "terraform"
    Project     = "sai-platform"
  }
}

# Networking Module
module "networking" {
  source = "../../modules/networking"

  compartment_id = var.compartment_id
  vcn_name       = "${local.name_prefix}-vcn"
  vcn_cidr       = "10.0.0.0/16"
  subnet_name    = "${local.name_prefix}-subnet"
  subnet_cidr    = "10.0.1.0/24"

  freeform_tags = local.common_tags
}

# Security Module
module "security" {
  source = "../../modules/security"

  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix
  turn_username  = var.turn_username
  turn_password  = var.turn_password

  freeform_tags = local.common_tags
}

# Container Registry Module
module "container_registry" {
  source = "../../modules/container-registry"

  compartment_id = var.compartment_id
  name_prefix    = local.name_prefix

  freeform_tags = local.common_tags
}

# Docker Build Module (builds and pushes images automatically)
module "docker_build" {
  source = "../../modules/docker-build"

  compartment_id            = var.compartment_id
  region                    = var.region
  project_root              = "${path.root}/../../.."
  signaling_server_repo_url = module.container_registry.signaling_server_repo_url
  turn_server_repo_url      = module.container_registry.turn_server_repo_url
  signaling_image_tag       = var.signaling_image_tag
  turn_image_tag            = var.turn_image_tag
  force_rebuild             = var.force_rebuild_images

  depends_on = [module.container_registry]
}



# Object Storage bucket for media server packages
resource "oci_objectstorage_bucket" "media_server_packages" {
  compartment_id = var.compartment_id
  name           = "sai-platform-dev-media-server-repo"
  namespace      = data.oci_objectstorage_namespace.current.namespace

  freeform_tags = local.common_tags
}

# Get current namespace
data "oci_objectstorage_namespace" "current" {
  compartment_id = var.compartment_id
}

# Media Server Module
module "media_server" {
  source = "../../modules/media-server"

  # OCI Configuration
  compartment_id = var.compartment_id
  tenancy_ocid   = var.tenancy_ocid
  name_prefix    = local.name_prefix

  # Instance Configuration
  instance_shape     = var.instance_shape
  instance_ocpus     = var.instance_ocpus
  instance_memory_gb = var.instance_memory_gb
  ssh_public_key     = var.ssh_public_key

  # Application Configuration
  package_name    = var.package_name
  package_version = var.package_version
  jwt_secret      = var.jwt_secret

  # Networking - use shared networking
  vcn_id    = module.networking.vcn_id
  subnet_id = module.networking.subnet_id

  # Tags
  freeform_tags = local.common_tags

  depends_on = [module.networking]
}

# Signaling Server Container Module
module "signaling_server" {
  source = "../../modules/signaling-server-container"

  compartment_id     = var.compartment_id
  name_prefix        = local.name_prefix
  vcn_id             = module.networking.vcn_id
  subnet_id          = module.networking.subnet_id
  instance_shape     = var.signaling_instance_shape
  instance_ocpus     = var.signaling_instance_ocpus
  instance_memory_gb = var.signaling_instance_memory_gb
  signaling_image    = module.docker_build.signaling_server_image_url
  media_server_url   = module.media_server.media_server_url
  turn_server_ip     = module.turn_server.public_ip
  turn_username      = var.turn_username
  turn_password      = var.turn_password
  jwt_secret         = var.jwt_secret
  mongodb_uri        = var.mongodb_uri

  freeform_tags = local.common_tags

  depends_on = [module.networking, module.media_server, module.turn_server, module.docker_build]
}

# TURN Server Container Module
module "turn_server" {
  source = "../../modules/turn-server-container"

  compartment_id     = var.compartment_id
  name_prefix        = local.name_prefix
  vcn_id             = module.networking.vcn_id
  subnet_id          = module.networking.subnet_id
  instance_shape     = var.turn_instance_shape
  instance_ocpus     = var.turn_instance_ocpus
  instance_memory_gb = var.turn_instance_memory_gb
  turn_image         = module.docker_build.turn_server_image_url
  turn_username      = var.turn_username
  turn_password      = var.turn_password

  freeform_tags = local.common_tags

  depends_on = [module.networking, module.docker_build]
}

# Client App Storage Module
module "client_app_storage" {
  source = "../../modules/client-app-storage"

  compartment_id       = var.compartment_id
  name_prefix          = local.name_prefix
  signaling_server_url = module.signaling_server.signaling_server_url
  region               = var.region

  freeform_tags = local.common_tags

  depends_on = [module.signaling_server]
}

# Client App Build and Deploy Module
module "client_app_build" {
  source = "../../modules/client-app-build"

  compartment_id       = var.compartment_id
  bucket_name          = module.client_app_storage.bucket_name
  bucket_namespace     = module.client_app_storage.bucket_namespace
  signaling_server_url = module.signaling_server.signaling_server_url
  region               = var.region
  environment          = local.environment

  depends_on = [module.client_app_storage]
}

# Load Balancer Module (provides OCI-native domain names)
module "load_balancers" {
  source = "../../modules/dns"

  compartment_id        = var.compartment_id
  name_prefix           = local.name_prefix
  domain_name           = var.domain_name
  # signaling_server_ip removed - container instance IPs not easily accessible
  subnet_id             = module.networking.subnet_id
  region                = var.region
  create_load_balancer  = var.create_load_balancer
  bucket_name           = module.client_app_storage.bucket_name
  bucket_namespace      = module.client_app_storage.bucket_namespace

  freeform_tags = local.common_tags

  depends_on = [module.signaling_server, module.networking, module.client_app_storage]
}
