#!/bin/bash

# Build and Push All Docker Images
# This script builds both signaling server and TURN server images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; exit 1; }

# Configuration from Terraform
SIGNALING_REGISTRY_URL="idqklxxi9ug5.ocir.io/sai-platform-dev/signaling-server"
TURN_REGISTRY_URL="idqklxxi9ug5.ocir.io/sai-platform-dev/turn-server"
SIGNALING_IMAGE_TAG="latest"
TURN_IMAGE_TAG="latest"
REGION="us-ashburn-1"
PROJECT_ROOT="./../../.."

log "Building all Docker images for SAI Platform..."
log "Project Root: ${PROJECT_ROOT}"
log "Region: ${REGION}"

# Check prerequisites
if ! command -v docker &> /dev/null; then
    error "Docker is not installed. Please install Docker first."
fi

if ! command -v oci &> /dev/null; then
    error "OCI CLI is not installed. Please install OCI CLI first."
fi

# Configure Docker authentication for OCI Container Registry
log "Configuring Docker authentication for OCI Container Registry..."
REGISTRY_ENDPOINT="us-ashburn-1.ocir.io"

# Use existing OCI session for Docker login
log "Attempting Docker login with existing OCI credentials (profile: mike)..."
export OCI_CLI_PROFILE=mike

if ! docker login "$$REGISTRY_ENDPOINT" 2>/dev/null; then
    log "Direct login failed, trying with OCI auth helper..."
    if ! oci session validate --profile mike 2>/dev/null; then
        error "OCI session expired or invalid. Please run 'oci session authenticate --region us-ashburn-1 --profile-name mike' first, then re-run terraform apply."
    fi

    # Try Docker login again with profile set
    if ! docker login "$$REGISTRY_ENDPOINT" 2>/dev/null; then
        error "Failed to authenticate with OCI Container Registry. Please ensure your OCI session is valid."
    fi
fi

# Build signaling server
log "Building signaling server..."
cd "${PROJECT_ROOT}"

if [ ! -f "Dockerfile" ]; then
    error "Signaling server Dockerfile not found in: ${PROJECT_ROOT}"
fi

SIGNALING_FULL_URL="${SIGNALING_REGISTRY_URL}:${SIGNALING_IMAGE_TAG}"
docker build --platform linux/amd64 -t "${SIGNALING_FULL_URL}" -f Dockerfile . || error "Failed to build signaling server"
docker push "${SIGNALING_FULL_URL}" || error "Failed to push signaling server"
success "Signaling server built and pushed: ${SIGNALING_FULL_URL}"

# Build TURN server
log "Building TURN server..."
cd "${PROJECT_ROOT}/turn-server"

if [ ! -f "Dockerfile" ]; then
    error "TURN server Dockerfile not found in: ${PROJECT_ROOT}/turn-server"
fi

TURN_FULL_URL="${TURN_REGISTRY_URL}:${TURN_IMAGE_TAG}"
docker build --platform linux/amd64 -t "${TURN_FULL_URL}" -f Dockerfile . || error "Failed to build TURN server"
docker push "${TURN_FULL_URL}" || error "Failed to push TURN server"
success "TURN server built and pushed: ${TURN_FULL_URL}"

# Clean up local images
log "Cleaning up local images..."
docker rmi "${SIGNALING_FULL_URL}" || warning "Failed to remove signaling server image (this is okay)"
docker rmi "${TURN_FULL_URL}" || warning "Failed to remove TURN server image (this is okay)"

success "All Docker images built and pushed successfully!"
log "Images available at:"
log "  Signaling Server: ${SIGNALING_FULL_URL}"
log "  TURN Server: ${TURN_FULL_URL}"
