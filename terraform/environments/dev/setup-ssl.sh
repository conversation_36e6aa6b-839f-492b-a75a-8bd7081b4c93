#!/bin/bash

# Script to set up SSL certificate on the deployed media server
# Usage: ./setup-ssl.sh

set -e

# Get the public IP from Terraform output
PUBLIC_IP=$(terraform output -raw media_server_public_ip 2>/dev/null || echo "")

if [ -z "$PUBLIC_IP" ]; then
    echo "❌ Could not get public IP from Terraform output"
    echo "💡 Make sure you're in the terraform/environments/oci-dev directory"
    echo "💡 And that the infrastructure is deployed"
    exit 1
fi

echo "🌐 Media server IP: $PUBLIC_IP"
echo "🔒 Setting up Let's Encrypt SSL certificate..."

# SSH into the server and run the SSL setup script
ssh -i ~/.ssh/oci_key -o StrictHostKeyChecking=no ubuntu@$PUBLIC_IP "sudo /opt/media-server/setup-ssl.sh"

if [ $? -eq 0 ]; then
    echo "✅ SSL certificate setup completed successfully!"
    echo "🌐 Your media server is now available at: https://media-server.$PUBLIC_IP.nip.io:8080"
    echo ""
    echo "🔧 Update your client configuration to use:"
    echo "   mediaServerUrl = 'https://media-server.$PUBLIC_IP.nip.io:8080';"
else
    echo "❌ SSL certificate setup failed"
    echo "📋 Check the logs on the server: ssh ubuntu@$PUBLIC_IP 'sudo tail -f /var/log/ssl-setup.log'"
    exit 1
fi
