# Oracle Cloud Infrastructure (OCI) Networking Guide for WebRTC

This document explains the critical networking requirements for deploying WebRTC media servers on Oracle Cloud Infrastructure, particularly the iptables rule ordering that is essential for proper operation.

## Critical iptables Rule Ordering

### ⚠️ **CRITICAL**: WebRTC Media Ports Must Be at Position #6

For WebRTC to work properly on OCI, the media ports (40000-40100) **MUST** be positioned at iptables rule #6. This is not arbitrary - it's required by Oracle Cloud's networking stack.

### Correct Rule Order

```bash
Chain INPUT (policy ACCEPT)
num  target     prot opt source               destination         
1    ACCEPT     all  --  0.0.0.0/0            0.0.0.0/0            state RELATED,ESTABLISHED
2    ACCEPT     icmp --  0.0.0.0/0            0.0.0.0/0           
3    ACCEPT     all  --  0.0.0.0/0            0.0.0.0/0           
4    ACCEPT     tcp  --  0.0.0.0/0            0.0.0.0/0            tcp dpt:80
5    ACCEPT     tcp  --  0.0.0.0/0            0.0.0.0/0            tcp dpt:8080
6    ACCEPT     tcp  --  0.0.0.0/0            0.0.0.0/0            tcp dpts:40000:40100  ← CRITICAL POSITION
7    ACCEPT     udp  --  0.0.0.0/0            0.0.0.0/0            udp dpts:40000:40100  ← CRITICAL POSITION
8    ACCEPT     udp  --  0.0.0.0/0            0.0.0.0/0            udp dpt:5004
9    ACCEPT     udp  --  0.0.0.0/0            0.0.0.0/0            udp dpt:5005
10   ACCEPT     udp  --  0.0.0.0/0            0.0.0.0/0            udp dpt:5006
11   ACCEPT     udp  --  0.0.0.0/0            0.0.0.0/0            udp dpt:5007
12   ACCEPT     tcp  --  0.0.0.0/0            0.0.0.0/0            state NEW tcp dpt:22
13   REJECT     all  --  0.0.0.0/0            0.0.0.0/0            reject-with icmp-host-prohibited
```

## Required Ports

### WebRTC Media Ports (Position 6-7)
- **TCP 40000-40100**: WebRTC media (fallback)
- **UDP 40000-40100**: WebRTC media (primary)

### Application Ports
- **TCP 80**: HTTP (Let's Encrypt challenges)
- **TCP 8080**: HTTPS Media Server
- **TCP 443**: HTTPS (optional)

### RTP Ports for FFmpeg (Position 8-11)
- **UDP 5004**: Audio RTP
- **UDP 5005**: Audio RTCP
- **UDP 5006**: Video RTP
- **UDP 5007**: Video RTCP

### System Ports
- **TCP 22**: SSH

## Why Position #6 is Critical

### Oracle Cloud Networking Stack
Oracle Cloud's networking infrastructure processes iptables rules in a specific way:

1. **Rules 1-5**: Essential system and application rules
2. **Rules 6-7**: **User application media traffic zone** ← WebRTC MUST be here
3. **Rules 8+**: Additional application rules
4. **Final rule**: REJECT all

### What Happens with Wrong Positioning

If WebRTC ports are positioned after rule #7, Oracle Cloud's networking stack may:
- Apply additional filtering before reaching the rules
- Interfere with ICE candidate establishment
- Block UDP hole punching required for WebRTC

### Historical Evidence
- **Working deployment**: WebRTC ports at position #6
- **Broken deployment**: WebRTC ports at position #10+
- **Fix**: Moving WebRTC ports back to position #6 immediately resolved issues

## Deployment Commands

### Correct iptables Setup
```bash
# Essential system rules (positions 1-5 are pre-configured)

# WebRTC media ports - MUST be at position 6-7
sudo iptables -I INPUT 6 -p tcp --dport 40000:40100 -j ACCEPT
sudo iptables -I INPUT 6 -p udp --dport 40000:40100 -j ACCEPT

# RTP ports for FFmpeg - positions 8-11
sudo iptables -I INPUT 8 -p udp --dport 5004 -j ACCEPT
sudo iptables -I INPUT 9 -p udp --dport 5005 -j ACCEPT  
sudo iptables -I INPUT 10 -p udp --dport 5006 -j ACCEPT
sudo iptables -I INPUT 11 -p udp --dport 5007 -j ACCEPT

# Save rules
sudo netfilter-persistent save
```

### Verification
```bash
# Check rule order
sudo iptables -L INPUT -n --line-numbers

# Verify WebRTC ports are at positions 6-7
sudo iptables -L INPUT -n --line-numbers | grep "40000:40100"
```

## Troubleshooting

### Symptoms of Wrong Rule Order
- WebRTC connections fail to establish
- ICE candidates cannot be gathered
- Media streams don't flow despite successful signaling
- Browser shows "failed to connect" errors

### Quick Fix
If WebRTC ports are in wrong positions:

```bash
# Remove incorrectly positioned rules
sudo iptables -D INPUT [wrong_position_number]

# Re-add at correct position
sudo iptables -I INPUT 6 -p tcp --dport 40000:40100 -j ACCEPT
sudo iptables -I INPUT 6 -p udp --dport 40000:40100 -j ACCEPT

# Save changes
sudo netfilter-persistent save
```

### Verification Commands
```bash
# Test WebRTC connectivity
curl -k https://your-media-server:8080/

# Check if ports are listening
sudo ss -tulnp | grep -E "40000|5004|5006|8080"

# Monitor traffic
sudo tcpdump -i any port 40000 or port 5004
```

## Security List Configuration

In addition to iptables, ensure OCI Security Lists allow:

```hcl
# WebRTC media ports
ingress_security_rules {
  protocol = "6" # TCP
  source   = "0.0.0.0/0"
  tcp_options {
    min = 40000
    max = 40100
  }
}

ingress_security_rules {
  protocol = "17" # UDP
  source   = "0.0.0.0/0"
  udp_options {
    min = 40000
    max = 40100
  }
}

# RTP ports for FFmpeg
ingress_security_rules {
  protocol = "17" # UDP
  source   = "0.0.0.0/0"
  udp_options {
    min = 5004
    max = 5007
  }
}
```

## Best Practices

1. **Always verify rule order** after any iptables changes
2. **Test WebRTC connectivity** after deployment
3. **Monitor logs** for ICE candidate failures
4. **Keep WebRTC ports at position 6-7** - never move them
5. **Document any changes** to networking configuration

## References

- [Oracle Cloud Networking Documentation](https://docs.oracle.com/en-us/iaas/Content/Network/Concepts/overview.htm)
- [WebRTC ICE Candidate Requirements](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API/Connectivity)
- [iptables Rule Processing Order](https://www.netfilter.org/documentation/HOWTO/packet-filtering-HOWTO-6.html)
