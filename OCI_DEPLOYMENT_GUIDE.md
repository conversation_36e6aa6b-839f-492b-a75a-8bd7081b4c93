# OCI Media Server Deployment Guide

This guide walks you through deploying the SAI Platform media server to Oracle Cloud Infrastructure (OCI) using Ampere A1 instances.

## Overview

The deployment includes:
- **Ampere A1 Instance**: ARM64-based compute (Always Free eligible)
- **OCI Artifact Registry**: Private package storage with versioned tgz files
- **WebRTC Optimization**: UDP traffic support for real-time streaming
- **Auto-configuration**: SSL certificates, firewall, and systemd service
- **Secure Deployment**: Authentication token-based package downloads

## Prerequisites

### 1. OCI Account Setup

1. Create an OCI account at [cloud.oracle.com](https://cloud.oracle.com)
2. Complete identity verification
3. Note your tenancy OCID and compartment OCID

### 2. OCI CLI Installation

```bash
# Install OCI CLI
bash -c "$(curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh)"

# Configure OCI CLI
oci setup config
```

During configuration, you'll need:
- User OCID
- Tenancy OCID  
- Region (e.g., us-ashburn-1)
- API key (will be generated)

### 3. SSH Key Pair

```bash
# Generate SSH key pair if you don't have one
ssh-keygen -t rsa -b 4096 -f ~/.ssh/oci_key

# Get public key content
cat ~/.ssh/oci_key.pub
```

## Package Preparation

The deployment uses OCI Artifact Registry to store versioned tgz packages of your media server. This approach is simpler and more secure than npm registries.

### Create Media Server Package

```bash
# From project root directory
./scripts/package-media-server.sh 1.0.0
```

This creates a `dist/media-server-1.0.0.tgz` file containing:
- `server.js` - Main media server application
- `config.js` and `config.production.js` - Configuration files
- `package.json` - Package metadata
- `node_modules/` - Dependencies (if present)
- `ssl/` - SSL certificates (if present)

### Package Versioning

The package filename includes the version for easy management:
- `media-server-1.0.0.tgz`
- `media-server-1.1.0.tgz`
- `media-server-2.0.0.tgz`

This allows you to:
- Deploy specific versions
- Rollback to previous versions
- Maintain multiple versions simultaneously

## Deployment Steps

### 1. Package Media Server

```bash
# From project root directory
./scripts/package-media-server.sh 1.0.0
```

### 2. Configure Terraform Variables

```bash
cd terraform/environments/oci-dev
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars`:

```hcl
# OCI Configuration (get from 'oci setup config')
tenancy_ocid     = "ocid1.tenancy.oc1..aaaaaaaa..."
user_ocid        = "ocid1.user.oc1..aaaaaaaa..."
fingerprint      = "aa:bb:cc:dd:ee:ff:gg:hh:ii:jj:kk:ll:mm:nn:oo:pp"
private_key_path = "~/.oci/oci_api_key.pem"
region           = "us-ashburn-1"
compartment_id   = "ocid1.compartment.oc1..aaaaaaaa..."

# SSH Configuration
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... your-public-key"

# Instance Configuration (Always Free eligible)
instance_ocpus     = 1    # Up to 4 OCPUs free
instance_memory_gb = 6    # Up to 24GB free

# Package Configuration
package_name    = "media-server"
package_version = "1.0.0"  # Must match packaged version
```

### 3. Deploy Infrastructure

```bash
# Deploy with script
./deploy.sh apply

# Or deploy manually
terraform init
terraform plan
terraform apply
```

### 4. Upload Package to Artifact Registry

```bash
# Get repository details
REPO_ID=$(terraform output -raw artifact_registry_info | jq -r '.repository_id')

# Upload the package
oci artifacts generic artifact upload-by-path \
  --repository-id "$REPO_ID" \
  --artifact-path "media-server-1.0.0.tgz" \
  --content-body "../../../dist/media-server-1.0.0.tgz" \
  --artifact-version "1.0.0"
```

### 5. Redeploy Media Server

```bash
# Redeploy to download and install the package
terraform apply -replace=module.oci_media_server.oci_core_instance.media_server
```

### 6. Verify Deployment

```bash
# Get deployment outputs
terraform output

# SSH to instance
ssh opc@$(terraform output -raw media_server_public_ip)

# Check media server status
sudo systemctl status media-server
sudo journalctl -u media-server -f
```

## ARM64 Compatibility Considerations

### Node.js Native Modules

Some npm packages may need recompilation for ARM64:

```bash
# If mediasoup fails to install, try:
npm install --build-from-source

# Or use pre-built binaries
npm config set target_arch arm64
npm config set target_platform linux
```

### Alternative Packages

If the main package fails, the startup script installs fallbacks:

```bash
# Fallback packages installed automatically
npm install express socket.io cors mediasoup ffmpeg-static
```

### FFmpeg Considerations

The media server uses `ffmpeg-static` which may have ARM64 compatibility issues:

```bash
# Alternative: Install system FFmpeg
sudo dnf install -y ffmpeg

# Update media server to use system FFmpeg instead of ffmpeg-static
```

## Troubleshooting

### 1. Package Installation Issues

```bash
# SSH to instance and check logs
ssh opc@YOUR_IP
sudo journalctl -u media-server -f

# Manually install package
cd /opt/media-server
sudo npm install @sai/media-server@latest
```

### 2. WebRTC Connection Issues

```bash
# Check firewall
sudo firewall-cmd --list-all

# Verify ports are open
sudo netstat -tulpn | grep :8080
sudo netstat -tulpn | grep :40000
```

### 3. ARM64 Compilation Issues

```bash
# Install additional build tools
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y python3-devel

# Rebuild native modules
cd /opt/media-server
sudo npm rebuild
```

## Cost Optimization

### Always Free Resources

OCI Always Free includes:
- 4 OCPUs Ampere A1 compute
- 24 GB RAM
- 200 GB block storage
- 10 TB outbound data transfer per month

### Recommended Configuration

```hcl
# Cost-effective configuration
instance_ocpus     = 2    # 2 ARM cores
instance_memory_gb = 12   # 12 GB RAM
```

## Security Best Practices

1. **SSH Keys**: Use key-based authentication only
2. **Firewall**: Only open required ports
3. **Updates**: Keep system packages updated
4. **Monitoring**: Monitor resource usage and logs

## Cleanup

```bash
# Destroy all resources
./deploy.sh destroy

# Or manually
terraform destroy
```

## Next Steps

1. **Domain Setup**: Configure DNS for your media server IP
2. **SSL Certificates**: Replace self-signed certs with Let's Encrypt
3. **Monitoring**: Set up OCI monitoring and alerting
4. **Backup**: Configure automated backups
5. **Load Balancing**: Add multiple instances behind OCI Load Balancer

## Support

For issues:
1. Check the startup script logs: `/var/log/startup-script.log`
2. Check media server logs: `sudo journalctl -u media-server -f`
3. Verify OCI configuration: `oci iam user get --user-id $(oci iam user list --query 'data[0].id' --raw-output)`
